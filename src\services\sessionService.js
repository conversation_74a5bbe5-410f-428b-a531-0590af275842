// User session management
const fs = require('fs');
const path = require('path');

// In-memory session storage for faster access
const sessions = new Map();

// Ensure data directory exists
const dataDir = path.join(__dirname, '../../data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

const sessionsFile = path.join(dataDir, 'sessions.json');

// Mutex lock for session saving to prevent file corruption
let isSaving = false;
let pendingSave = false;

// Load existing sessions from file - don't block startup
try {
  if (fs.existsSync(sessionsFile)) {
    const data = fs.readFileSync(sessionsFile, 'utf8');
    const loadedSessions = JSON.parse(data);
    Object.entries(loadedSessions).forEach(([userId, session]) => {
      sessions.set(userId, session);
    });
    console.log(`Loaded ${sessions.size} sessions from file`);
  }
} catch (error) {
  console.error('Error loading sessions:', error);
}

/**
 * Get a user's session data - synchronous version
 * @param {string|number} userId - User ID
 * @returns {object|null} Session data or null if not found
 */
function getSession(userId) {
  const userIdStr = userId.toString();
  const session = sessions.get(userIdStr);
  return session ? JSON.parse(JSON.stringify(session)) : null;
}

/**
 * Save session data for a user
 * @param {string|number} userId - User ID
 * @param {object} sessionData - Session data to save
 */
function saveSession(userId, sessionData) {
  const userIdStr = userId.toString();
  
  // If sessionData is null, delete the session
  if (sessionData === null) {
    sessions.delete(userIdStr);
  } else {
    // Create a deep copy to avoid reference issues
    const sessionCopy = JSON.parse(JSON.stringify(sessionData));
    sessions.set(userIdStr, sessionCopy);
  }
  
  // Save to file (debounced) - don't block
  debouncedSave();
  return sessionData; // Return the saved session for convenience
}

/**
 * Get a clean copy of the session by userId
 * This ensures we're working with a fresh copy and not a reference
 * @param {string|number} userId - User ID
 * @returns {object|null} Fresh copy of session data or null
 */
function getFreshSession(userId) {
  const userIdStr = userId.toString();
  const session = sessions.get(userIdStr);
  if (!session) return null;
  
  // Return a deep copy
  return JSON.parse(JSON.stringify(session));
}

/**
 * Update specific fields in a session without overwriting the entire session
 * @param {string|number} userId - User ID
 * @param {object} updates - Fields to update
 * @returns {object|null} Updated session or null if session doesn't exist
 */
function updateSession(userId, updates) {
  const userIdStr = userId.toString();
  const currentSession = getSession(userIdStr);
  
  if (!currentSession) {
    return null;
  }
  
  // Merge the updates with current session
  const updatedSession = {
    ...currentSession,
    ...updates
  };
  
  // Save the updated session
  return saveSession(userIdStr, updatedSession);
}

// Debounce save operations to avoid excessive disk writes
let saveTimeout = null;
function debouncedSave() {
  if (saveTimeout) {
    clearTimeout(saveTimeout);
  }
  
  // Set pendingSave flag so we know to save again if currently saving
  pendingSave = true;
  
  saveTimeout = setTimeout(() => {
    if (!isSaving) {
      saveToDisk();
    }
  }, 1000); // 1 second debounce
}

// Non-blocking save to disk
function saveToDisk() {
  if (isSaving) {
    return;
  }
  
  isSaving = true;
  pendingSave = false;
  
  // Prepare data - do this inside the mutex to get a consistent snapshot
  const sessionObj = {};
  for (const [userId, session] of sessions.entries()) {
    sessionObj[userId] = session;
  }
  
  // Save asynchronously
  fs.writeFile(sessionsFile, JSON.stringify(sessionObj, null, 2), (err) => {
    isSaving = false;
    
    if (err) {
      console.error('Error saving sessions to disk:', err);
    } else {
      console.log(`Saved ${sessions.size} sessions to disk`);
    }
    
    // If another save was requested while we were saving, trigger another save
    if (pendingSave) {
      debouncedSave();
    }
  });
}

/**
 * Check if a user is awaiting a suggestion
 * @param {string|number} userId - User ID
 * @returns {boolean} True if user is awaiting a suggestion
 */
function isAwaitingSuggestion(userId) {
  const session = getSession(userId);
  return session && session.awaitingSuggestion === true;
}

/**
 * Save quiz results for a user
 * @param {string|number} userId - User ID
 * @param {object} results - Quiz results to save
 */
function saveQuizResults(userId, results) {
  const session = getSession(userId) || {};
  
  if (!session.quizHistory) {
    session.quizHistory = [];
  }
  
  // Add timestamp if not provided
  if (!results.timestamp) {
    results.timestamp = Date.now();
  }
  
  // Add to quiz history
  session.quizHistory.push(results);
  
  // Keep only the last 10 quiz results
  if (session.quizHistory.length > 10) {
    session.quizHistory = session.quizHistory.slice(-10);
  }
  
  saveSession(userId, session);
  return session;
}

module.exports = {
  getSession,
  getFreshSession,
  saveSession,
  updateSession,
  isAwaitingSuggestion,
  saveQuizResults
}; 