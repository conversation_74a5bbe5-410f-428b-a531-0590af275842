/**
 * User service for managing user-related operations
 */
const database = require('../database/database');
const logger = require('../utils/logger');

/**
 * Get user information by ID
 * @param {string} userId - The user ID
 * @returns {Promise<Object|null>} - User object or null if not found
 */
async function getUserById(userId) {
  try {
    const db = database.db();
    
    if (!db) {
      logger.error('Database not initialized in getUserById');
      return null;
    }
    
    return new Promise((resolve) => {
      db.get('SELECT * FROM users WHERE id = ?', [userId], (err, row) => {
        if (err) {
          logger.error(`Error getting user ${userId}: ${err.message}`);
          resolve(null);
          return;
        }
        
        resolve(row || null);
      });
    });
  } catch (error) {
    logger.error(`Error in getUserById: ${error.message}`);
    return null;
  }
}

/**
 * Track user activity
 * @param {string} userId - User ID
 * @param {string} userName - User name
 * @param {string} action - Action performed
 */
async function trackUserActivity(userId, userName, action) {
  try {
    const db = database.db();
    
    if (!db) {
      logger.debug('Database not initialized in trackUserActivity');
      return;
    }
    
    // First ensure user exists in users table
    db.run(
      'INSERT OR IGNORE INTO users (id, username, first_seen) VALUES (?, ?, ?)',
      [userId, userName || 'unknown', Date.now()],
      (err) => {
        if (err) {
          logger.error(`Error inserting user: ${err.message}`);
        }
        
        // Now log the activity
        db.run(
          'INSERT INTO user_activity (user_id, action, timestamp) VALUES (?, ?, ?)',
          [userId, action, Date.now()],
          (activityErr) => {
            if (activityErr) {
              logger.error(`Error tracking activity: ${activityErr.message}`);
            }
          }
        );
      }
    );
  } catch (error) {
    logger.error(`Error in trackUserActivity: ${error.message}`);
  }
}

/**
 * Get user stats (counts of different activities)
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - User stats
 */
async function getUserStats(userId) {
  const stats = {
    totalQuestions: 0,
    mcqQuestions: 0,
    tfQuestions: 0,
    lastActivity: 0
  };
  
  try {
    const db = database.db();
    
    if (!db) {
      logger.error('Database not initialized in getUserStats');
      return stats;
    }
    
    return new Promise((resolve) => {
      // Get question generation counts
      db.all(
        `SELECT 
          COUNT(*) as total,
          SUM(CASE WHEN action LIKE 'generate_MCQ%' THEN 1 ELSE 0 END) as mcq,
          SUM(CASE WHEN action LIKE 'generate_TF%' THEN 1 ELSE 0 END) as tf,
          MAX(timestamp) as last_activity
        FROM user_activity
        WHERE user_id = ?`,
        [userId],
        (err, rows) => {
          if (err) {
            logger.error(`Error getting user stats: ${err.message}`);
            resolve(stats);
            return;
          }
          
          if (rows && rows.length > 0) {
            stats.totalQuestions = rows[0].total || 0;
            stats.mcqQuestions = rows[0].mcq || 0;
            stats.tfQuestions = rows[0].tf || 0;
            stats.lastActivity = rows[0].last_activity || 0;
          }
          
          resolve(stats);
        }
      );
    });
  } catch (error) {
    logger.error(`Error in getUserStats: ${error.message}`);
    return stats;
  }
}

module.exports = {
  getUserById,
  trackUserActivity,
  getUserStats
}; 