# Telegram MCQ/TF Bot - Migration Guide

This guide will help you migrate the Telegram MCQ/TF Question Generator bot to a new Windows PC.

## Prerequisites

- Windows 10 or later
- Node.js (v14+ recommended)
- Python 3.8+ with pip
- Git (optional, for cloning the repository)
- A Telegram bot token (from @BotFather)
- OpenRouter API key (for AI model access)

## Step 1: Copy the Files

You have two options:

### Option A: Copy the Entire Directory
1. Copy the entire `MCQ_TF` folder to your new PC
2. Place it in a location like `C:\\Users\\<USER>\\Desktop\\` or wherever you prefer

### Option B: Clone from Git (if using version control)
1. Install Git on your new PC if not already installed
2. Open Command Prompt or PowerShell
3. Navigate to where you want to place the project: `cd C:\\Users\\<USER>\\Desktop\\`
4. Clone the repository: `git clone [repository-url] MCQ_TF`
5. Navigate into the project directory: `cd MCQ_TF`

## Step 2: Install Node.js

1. Download Node.js from [nodejs.org](https://nodejs.org/) (LTS version recommended)
2. Run the installer, following the default installation options
3. Verify installation by opening Command Prompt and typing:
   ```
   node --version
   npm --version
   ```

## Step 3: Install Python and Dependencies

1. Download Python 3.8+ from [python.org](https://www.python.org/downloads/)
2. During installation:
   - ✅ Check "Add Python to PATH" (very important!)
   - ✅ Check "Install for all users"
   - Complete the installation

3. Verify Python installation:
   ```
   python --version
   ```

4. Install Python dependencies:
   ```
   cd MCQ_TF
   pip install -r requirements.txt
   ```

## Step 4: Install Tesseract OCR

Tesseract is required for extracting text from images and scanned documents:

1. Download the Tesseract installer from [UB-Mannheim's GitHub](https://github.com/UB-Mannheim/tesseract/wiki)
2. Run the installer
3. **Important**: Install to the default location (`C:\\Program Files\\Tesseract-OCR\\`)
4. During installation, select "English" and "Arabic" under Additional language data
5. Complete the installation

## Step 5: Set Up Environment Variables

1. Create a `.env` file in the project root using the template provided:
   ```
   copy env.windows.example .env
   ```

2. Edit the `.env` file with your details:
   - Set your Telegram bot token as `TELEGRAM_BOT_TOKEN`
   - Set your admin Telegram ID as `ADMIN_IDS`
   - Set your OpenRouter API key as `API_KEY`
   - Adjust paths if necessary (particularly `TESSERACT_PATH` if you installed Tesseract to a non-default location)

## New Configurable Paths

We've improved the system to make paths configurable through environment variables:

| Environment Variable | Description | Default Value |
|---------------------|-------------|---------------|
| `PYTHON_PATH` | Path to the Python executable | `python` |
| `TESSERACT_PATH` | Path to Tesseract OCR executable | `C:\\Program Files\\Tesseract-OCR\\tesseract.exe` |
| `TEMP_DIR` | Directory for temporary files | `./temp` |
| `LOGS_DIR` | Directory for log files | `./logs` |

You can customize these paths in the `.env` file if your installation locations differ from the defaults.

## Step 6: Install Node.js Dependencies

1. Open Command Prompt or PowerShell
2. Navigate to the project directory: `cd path\to\MCQ_TF`
3. Install dependencies:
   ```
   npm install
   ```

## Step 7: Create Required Directories

Some directories may need to be created manually:

```
mkdir data
mkdir logs
mkdir temp
mkdir uploads
```

## Step 8: Run the Bot

1. Start the bot:
   ```
   npm start
   ```
   
2. Alternatively, you can use the included `start.bat` file:
   ```
   start.bat
   ```

## Troubleshooting

### Database Initialization
- If you receive database errors, ensure the `data` directory exists
- The database will be created automatically on first run

### Python Extraction Issues
- If you encounter Python extraction errors, check that:
  - Python is properly installed and in PATH
  - Tesseract OCR is properly installed
  - The `TESSERACT_PATH` in your `.env` file points to the correct location

### Rate Limit Issues
- If you hit rate limits with the AI models, adjust the `MAX_CONCURRENT_REQUESTS` and `MAX_REQUESTS_PER_MODEL` values in your `.env` file
- Consider adding more model options in the `MODELS` environment variable

## Regular Maintenance

1. Clean temporary files: Run `cleanup.bat` occasionally to remove old temporary files
2. Update dependencies: Run `npm update` periodically to keep libraries updated
3. Check logs: Review files in the `logs` directory if you encounter issues

## Need Help?

If you encounter any issues during migration, please reach out for assistance.

Good luck with your migration! 