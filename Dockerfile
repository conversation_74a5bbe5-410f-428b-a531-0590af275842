FROM python:3.10-slim

WORKDIR /app

# Install Node.js and required system packages
RUN apt-get update && \
    apt-get install -y curl \
                       tesseract-ocr \
                       tesseract-ocr-ara \
                       tesseract-ocr-eng \
                       tesseract-ocr-all \
                       poppler-utils \
                       libreoffice \
                       build-essential \
                       libpoppler-cpp-dev \
                       pkg-config \
                       python3-dev && \
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y nodejs

# Set Tesseract data directory environment variable
ENV TESSDATA_PREFIX=/usr/share/tesseract-ocr/4.00/tessdata

# Create link to tessdata in app directory for local fallback
RUN mkdir -p /app/tessdata && \
    cp -R /usr/share/tesseract-ocr/4.00/tessdata/* /app/tessdata/ || \
    cp -R /usr/share/tesseract-ocr/tessdata/* /app/tessdata/ || echo "Could not copy tessdata files"

# Copy requirements first for better caching
COPY requirements.txt ./

# Install all Python dependencies from requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Node.js dependencies
COPY package*.json ./
RUN npm install

# Copy app files
COPY . .

# Create necessary directories with proper permissions
RUN mkdir -p /app/data /app/uploads /app/extracted && \
    chmod -R 777 /app/data /app/uploads /app/extracted /app/tessdata

# Create README for extraction setup
RUN echo "# Python Extraction Setup\n\nThis application requires Python and Tesseract OCR for document processing.\n\nTesseract language files are installed in:\n- /usr/share/tesseract-ocr/4.00/tessdata/ (main)\n- /app/tessdata/ (backup)\n\nIf you're seeing extraction errors, check that:\n1. Tesseract OCR is installed\n2. Language data files exist in one of the above directories\n3. The TESSDATA_PREFIX environment variable is set correctly\n" > /app/PYTHON_EXTRACTION_README.md

# Start the app
CMD ["npm", "start"]
