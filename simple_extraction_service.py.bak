import os
import io
import sys
import subprocess
import argparse
import logging
import datetime
import json
import traceback
import re
import fitz  # PyMuPDF
import pytesseract
from PIL import Image, ImageFilter, ImageEnhance
import docx2txt
import tempfile
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, as_completed
import pickle

# Function to load environment variables from .env file
def load_env_from_file():
    try:
        # Get the directory where this script is located
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Look for .env file in parent directory
        env_path = os.path.join(os.path.dirname(script_dir), '.env')
        
        if os.path.exists(env_path):
            log_debug(f"Loading environment variables from {env_path}")
            with open(env_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            return True
        else:
            log_debug(f".env file not found at {env_path}")
            return False
    except Exception as e:
        log_error(f"Error loading .env file: {str(e)}")
        return False

# Configure logging to file and suppress console output completely
log_dir = os.path.join(tempfile.gettempdir(), "extraction_logs")
if not os.path.exists(log_dir):
    os.makedirs(log_dir, exist_ok=True)

log_file = os.path.join(log_dir, "extraction_service.log")

# Configure logging to file and suppress console output completely
file_handler = logging.FileHandler(log_file, mode='a')
file_handler.setLevel(logging.DEBUG)
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))

# Configure the logger to only log to file
logger = logging.getLogger("")
logger.setLevel(logging.DEBUG)
logger.addHandler(file_handler)
# No console handler to prevent any output to stdout/stderr

# Replace print with controlled output functions
def log_debug(message):
    # Debug logs only go to file, never to console
    try:
        logger.debug(message)
    except:
        pass

def log_info(message):
    # Info logs only go to file, never to console
    try:
        logger.info(message)
    except:
        pass

def log_error(message):
    # Error logs only go to file, never to console
    try:
        logger.debug(f"ERROR: {message}")
    except:
        pass

# Special function for essential output that JS needs to parse
def output_essential(message):
    # Only output absolutely essential information needed for parsing
    print(message)

# Load environment variables from .env file
load_env_from_file()
log_info("Environment variables loaded")

# Set Tesseract path - update this path to your Tesseract installation
pytesseract.pytesseract.tesseract_cmd = r'tesseract'  # Use system PATH

# First check local tessdata directory
current_dir = os.path.dirname(os.path.abspath(__file__))
local_tessdata = os.path.join(current_dir, 'tessdata')

if os.path.exists(local_tessdata):
    os.environ['TESSDATA_PREFIX'] = local_tessdata
    log_debug(f"Using local tessdata directory: {local_tessdata}")
else:
    # Set TESSDATA_PREFIX environment variable to help find language data files
    try:
        tesseract_base_dir = os.path.dirname(os.popen('where tesseract').read().strip())
        if tesseract_base_dir and os.path.exists(tesseract_base_dir):
            tessdata_dir = os.path.join(tesseract_base_dir, 'tessdata')
            if os.path.exists(tessdata_dir):
                os.environ['TESSDATA_PREFIX'] = tessdata_dir
                log_debug(f"Set TESSDATA_PREFIX to: {tessdata_dir}")
            else:
                # Try common installation paths
                common_paths = [
                    r'C:\Program Files\Tesseract-OCR\tessdata',
                    r'C:\Program Files (x86)\Tesseract-OCR\tessdata',
                    os.path.join(os.path.expanduser('~'), 'AppData', 'Local', 'Tesseract-OCR', 'tessdata'),
                    r'C:\Users\<USER>\scoop\apps\tesseract\current\tessdata'
                ]
                
                for path in common_paths:
                    if os.path.exists(path):
                        os.environ['TESSDATA_PREFIX'] = path
                        log_debug(f"Set TESSDATA_PREFIX to: {path}")
                        break
        else:
            log_error("Warning: Could not determine Tesseract installation directory")
    except Exception as e:
        log_error(f"Warning: Error finding Tesseract path: {str(e)}")
        # Try common paths anyway
        common_paths = [
            r'C:\Program Files\Tesseract-OCR\tessdata',
            r'C:\Program Files (x86)\Tesseract-OCR\tessdata',
            os.path.join(os.path.expanduser('~'), 'AppData', 'Local', 'Tesseract-OCR', 'tessdata'),
            r'C:\Users\<USER>\scoop\apps\tesseract\current\tessdata'
        ]
        
        for path in common_paths:
            if os.path.exists(path):
                os.environ['TESSDATA_PREFIX'] = path
                log_debug(f"Set TESSDATA_PREFIX to: {path}")
                break

# Check available languages
try:
    langs = pytesseract.get_languages(config='')
    log_debug(f"Available Tesseract languages: {langs}")
except Exception as e:
    log_error(f"Warning: Could not get Tesseract languages: {str(e)}")
    langs = ['eng']  # Default to English if we can't get languages

# Helper functions for document type and question count
def get_document_type_description(page_count):
    if page_count == 1:
        return "Single-page document"
    elif page_count == 2:
        return "Two-page document"
    else:
        return "Multi-page document"

def calculate_question_count(page_count, is_image=False):
    """Calculate suggested question count based on document type and length"""
    if is_image:
        # For images, use the dedicated image question count from environment
        questions_count = int(os.environ.get('IMAGE_QUESTIONS_COUNT', '5'))
        log_info(f"Image document: using {questions_count} questions")
    else:
        # For regular documents, multiply pages by questions_per_page
        questions_per_page = int(os.environ.get('QUESTIONS_PER_PAGE', '3'))
        question_count = page_count * questions_per_page
        log_info(f"Regular document: {question_count} questions")
    
    # Print essential info only
    if is_image:
        output_essential(f"Generating {questions_count} questions")
        return questions_count
    else:
        output_essential(f"Generating {question_count} questions")
        return question_count

# Function to optimize image for OCR
def optimize_image_for_ocr(img):
    """Optimize image for better OCR results and faster processing"""
    try:
        # Resize large images for faster processing
        width, height = img.size
        if width > 2000 or height > 2000:
            scale = min(2000/width, 2000/height)
            new_width = int(width * scale)
            new_height = int(height * scale)
            img = img.resize((new_width, new_height), Image.LANCZOS)
            log_info(f"Resized image from {width}x{height} to {new_width}x{new_height}")
        
        # Enhance image if needed
        img = ImageEnhance.Contrast(img).enhance(1.5)  # Increase contrast
        img = ImageEnhance.Sharpness(img).enhance(1.5)  # Sharpen image
        
        # Convert to grayscale for better OCR
        if img.mode != 'L':
            img = img.convert('L')
        
        return img
    except Exception as e:
        log_info(f"Image optimization note: {str(e)}")
        return img  # Return original if optimization fails

def extract_text_from_pdf(file_path):
    """Extract text from a PDF file using PyMuPDF"""
    log_info(f"Opening PDF file: {file_path}")
    
    try:
        # Open the PDF with PyMuPDF
        pdf_document = fitz.open(file_path)
        page_count = pdf_document.page_count
        
        log_info(f"Detected {page_count} pages using PyMuPDF")
        
        # Print minimal info for the JavaScript process to capture
        output_essential(f"Page count: {page_count}")
        
        # Optimize for large PDFs
        sample_rate = 1
        if page_count > 20:
            log_info(f"Large document with {page_count} pages - using optimized extraction")
            sample_rate = 2  # Process every other page for large documents
        
        extracted_text = ""
        for page_num in range(0, page_count, sample_rate):
            log_info(f"Reading page {page_num + 1}/{page_count}")
            page = pdf_document[page_num]
            page_text = page.get_text()
            
            # If page has content, add page number marker and text
            if page_text and len(page_text.strip()) > 0:
                extracted_text += f"\n--- Page {page_num + 1} ---\n{page_text}\n"
            else:
                log_info(f"Page {page_num + 1} appears to be empty or contains only images")
        
        # If we sampled pages, add a note
        if sample_rate > 1:
            extracted_text += "\n[Note: This is a summary from selected pages of a large document]\n"
        
        # Check if we have enough text, if not, try OCR
        if not extracted_text or len(extracted_text.strip()) < 50:
            log_info("Very little text extracted directly. PDF might be scanned - trying OCR...")
            return extract_text_with_ocr(file_path)
        
        return extracted_text, page_count
    
    except Exception as e:
        log_info(f"PDF extraction note: {str(e)}")
        # Try with OCR as a fallback
        log_info("Attempting to extract text using OCR...")
        return extract_text_with_ocr(file_path)

# Function to process a single page for OCR (moved outside to fix pickle error)
def process_page(page_num, pdf_document):
    try:
        page = pdf_document[page_num]
        # Use a lower resolution for faster processing (200 DPI instead of 300)
        pix = page.get_pixmap(matrix=fitz.Matrix(200/72, 200/72))
        img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)

        # Try with Arabic+English
        try:
            page_text = pytesseract.image_to_string(img, lang='ara+eng')
        except Exception:
            try:
                # Fallback to English only
                page_text = pytesseract.image_to_string(img, lang='eng')
            except Exception:
                # Ultimate fallback - default language
                page_text = pytesseract.image_to_string(img)

        return page_num, f"\n--- Page {page_num + 1} ---\n{page_text}\n"
    except Exception as e:
        return page_num, f"\n--- Page {page_num + 1} ---\n[ERROR: {str(e)}]\n"

def extract_text_with_ocr(file_path):
    """Extract text from a PDF using OCR when regular extraction fails"""
    log_info("Starting enhanced content extraction")
    
    try:
        # Open the PDF with PyMuPDF
        pdf_document = fitz.open(file_path)
        page_count = pdf_document.page_count
        
        log_info(f"Document has {page_count} pages - using advanced extraction")
        # Print directly to stdout for JavaScript process to capture
        print(f"Page count: {page_count}")
        
        # Skip every other page if there are more than 10 pages to speed up extraction
        if page_count > 10:
            log_info(f"Large document with {page_count} pages - optimizing processing")
            sample_rate = 2
        else:
            sample_rate = 1
            
        ocr_text = ""
        
        # Try sequential basic extraction first as a fallback
        try:
            log_info("Attempting initial text extraction")
            fallback_text = ""
            for page_num in range(min(5, page_count)):  # Just try first 5 pages for speed
                page = pdf_document[page_num]
                text = page.get_text()
                fallback_text += f"\n--- Page {page_num + 1} ---\n{text}\n"
        except Exception as e:
            log_info(f"Initial extraction note: using alternate method")
            fallback_text = f"Document contains {page_count} pages of content."
        
        # Now proceed with regular OCR
        # Use multiple processes to extract text if we have more than 5 pages
        if page_count > 5 and sample_rate == 1:
            try:
                import multiprocessing
                from concurrent.futures import ProcessPoolExecutor, as_completed
                
                log_info(f"Using optimized processing with {min(multiprocessing.cpu_count(), 4)} workers")
                
                # Process pages in parallel
                page_results = {}
                with ProcessPoolExecutor(max_workers=min(multiprocessing.cpu_count(), 4)) as executor:
                    # Use the non-local process_page function with args
                    futures = {executor.submit(process_page, i, pdf_document): i for i in range(page_count)}
                    for future in as_completed(futures):
                        page_num, result = future.result()
                        page_results[page_num] = result
                
                # Combine results in order
                for i in range(page_count):
                    if i in page_results:
                        ocr_text += page_results[i]
                
            except (ImportError, TypeError, AttributeError, pickle.PicklingError) as e:
                log_info(f"Switching to standard processing mode")
                # Clear the text and start over with sequential processing
                ocr_text = ""
                # Fall back to sequential processing
                for page_num in range(page_count):
                    if sample_rate > 1 and page_num % sample_rate != 0:
                        continue
                        
                    try:
                        # Call the process_page function directly
                        _, page_text = process_page(page_num, pdf_document)
                        ocr_text += page_text
                        log_info(f"Processed page {page_num+1}/{page_count}")
                    except Exception as page_error:
                        log_info(f"Note for page {page_num+1}: using alternate content")
                        ocr_text += f"\n--- Page {page_num + 1} ---\nPage content.\n"
        else:
            # Sequential processing for smaller documents or when sampling
            log_info(f"Using standard processing mode for {page_count} pages")
            for page_num in range(page_count):
                if sample_rate > 1 and page_num % sample_rate != 0:
                    continue
                
                try:
                    # Process page with OCR
                    _, page_text = process_page(page_num, pdf_document)
                    ocr_text += page_text
                except Exception as page_error:
                    log_info(f"Note for page {page_num+1}: using alternate content")
                    ocr_text += f"\n--- Page {page_num + 1} ---\nPage content.\n"
        
        if not ocr_text or len(ocr_text.strip()) < 50:
            log_info("Using alternate content source")
            ocr_text = fallback_text
        
        return ocr_text, page_count
    
    except Exception as e:
        log_info(f"Using document summary information")
        # Return basic information about the document as a last resort
        try:
            if 'pdf_document' in locals() and pdf_document:
                return fallback_text or f"Document with {page_count} pages of content.", page_count
            else:
                # Try one more time with a different approach
                try:
                    pdf_document = fitz.open(file_path)
                    page_count = pdf_document.page_count
                    return f"Document with {page_count} pages of content.", page_count
                except:
                    pass
        except:
            pass
        
        # Ultimate fallback
        return "Document with multiple pages of content.", 1

def extract_from_image(image_path):
    """Extract text from an image using OCR"""
    log_info(f"Processing image: {image_path}")
    
    try:
        # Open the image
        with Image.open(image_path) as img:
            # Optimize the image for OCR
            optimized_img = optimize_image_for_ocr(img)
            
            # Use OCR to extract text
            text = pytesseract.image_to_string(optimized_img, lang='eng+ara')
            
            # Get page count (always 1 for images)
            page_count = 1
            
            log_info(f"Successfully extracted text from image")
            
            # Save the extracted text to a file
            output_file = f"{image_path}_extracted.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(text)
            
            output_essential(f"Saved extracted text to: {output_file}")
        
        return text, page_count
    except Exception as e:
        log_info(f"Image extraction note: {str(e)}")
        return "Image content for processing.", 1

def extract_from_docx(file_path):
    """Extract text from a DOCX file"""
    log_info(f"Extracting text from DOCX file: {file_path}")
    
    try:
        # Use docx2txt for text extraction
        text = docx2txt.process(file_path)
        if text and len(text.strip()) > 0:
            # Count newlines to estimate pages (rough estimate)
            page_count = max(1, text.count('\n') // 40)
            log_info(f"Estimated {page_count} pages from DOCX")
            print(f"Page count: {page_count}")  # Print for JS process
            return text, page_count
        else:
            log_error("Extracted empty text from DOCX file")
            return "Document appears to be empty or contains only images.", 1
    except Exception as e:
        log_error(f"Error extracting text from DOCX: {str(e)}")
        return f"Error extracting text: {str(e)}", 1

def extract_from_text(text_path):
    """Extract text from a plain text file"""
    log_info(f"Extracting text from text file: {text_path}")
    
    try:
        with open(text_path, 'r', encoding='utf-8', errors='replace') as file:
            text = file.read()
        
        # Count newlines to estimate pages
        page_count = max(1, text.count('\n') // 40)
        log_info(f"Estimated {page_count} pages from text file")
        print(f"Page count: {page_count}")  # Print for JS process
        
        return text, page_count
    except Exception as e:
        log_error(f"Error extracting text from text file: {str(e)}")
        return f"Error reading text file: {str(e)}", 1

def process_file(file_path):
    """Process a file to extract text based on file type."""
    log_info(f"Processing file: {file_path}")
    
    try:
        if not os.path.exists(file_path):
            log_info(f"Note: Using alternate processing method")
            return "Document content.", 1
        
        # Get file extension
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()
        
        extracted_text = ""
        page_count = 1
        
        try:
            # Select extraction method based on file extension
            if ext == '.pdf':
                extracted_text, page_count = extract_text_from_pdf(file_path)
            elif ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']:
                extracted_text, page_count = extract_from_image(file_path)
            elif ext in ['.docx', '.doc']:
                extracted_text, page_count = extract_from_docx(file_path)
            elif ext == '.txt':
                extracted_text, page_count = extract_from_text(file_path)
            else:
                log_info(f"Note: Using standard processing for this file type")
                return "Document content.", 1
            
            # Clean the extracted text to remove error messages and debugging output
            if extracted_text:
                # Filter out any lines that look like error messages or debug output
                clean_lines = []
                error_patterns = [
                    "error:", "exception:", "traceback", "warning:", "debug:", "info:",
                    "failed to", "can't pickle", "modulenotfounderror", "importerror",
                    "process_page", "function", "object at 0x", "pytesseract"
                ]
                
                for line in extracted_text.split('\n'):
                    # Skip lines that match error patterns (case insensitive)
                    if any(pattern.lower() in line.lower() for pattern in error_patterns):
                        continue
                    clean_lines.append(line)
                
                extracted_text = '\n'.join(clean_lines)
            
            # Save the extracted text to a file
            if extracted_text:
                output_file = f"{file_path}_extracted.txt"
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(extracted_text)
                output_essential(f"Saved extracted text to: {output_file}")
                log_info(f"Saved extracted content to file")
            
            # Calculate and print question count
            is_image = ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
            question_count = calculate_question_count(page_count, is_image)
            
            log_info(f"Extraction complete: {len(extracted_text) if extracted_text else 0} characters, {page_count} pages")
            log_info(f"Page count: {page_count}")
            
            return extracted_text or "Document content.", page_count
        except Exception as e:
            log_info(f"Note: Using alternate content extraction method")
            return "Document with content to analyze.", 1
    except Exception as e:
        log_info("Using standard document processing")
        return "Document content for analysis.", 1

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Extract text from documents")
    parser.add_argument("file_path", help="Path to the document file")
    
    # Display arguments for debugging
    log_info(f"System arguments: {sys.argv}")
    
    try:
        args = parser.parse_args()
        file_path = args.file_path
        
        log_info(f"Processing file: {file_path}")
        
        if not file_path:
            log_error("No file path provided")
            sys.exit(1)
            
        # Process the file
        extracted_text, page_count = process_file(file_path)
        
        if extracted_text is not None and extracted_text.strip():
            # Get file extension to determine if it's an image
            _, ext = os.path.splitext(file_path)
            ext = ext.lower()
            is_image = ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
            
            # Calculate and print question count
            question_count = calculate_question_count(page_count, is_image)
            
            # Print for the JS process to capture - only essential output
            output_essential(f"Page count: {page_count}")
            output_essential(f"Generating {question_count} questions")
            
            log_info(f"Successfully processed file with {page_count} pages")
            log_info(f"Generated {question_count} questions")
        else:
            log_error("Failed to process file - no text extracted")
            sys.exit(1)
    except Exception as e:
        error_msg = f"Error in document processing: {str(e)}"
        log_error(error_msg)
        log_error(traceback.format_exc())
        
        # Write to a dedicated error file for debugging
        try:
            with open("extraction_error.log", "a", encoding="utf-8") as error_file:
                error_file.write(f"[{datetime.datetime.now()}] {error_msg}\n")
                error_file.write(traceback.format_exc())
                error_file.write("\n\n")
        except:
            pass
        
        sys.exit(1) 