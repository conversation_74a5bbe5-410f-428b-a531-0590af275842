# Telegram Bot Configuration
# ======================
# Your bot token from BotFather
TELEGRAM_BOT_TOKEN=123456789:ABCdefGhIJKlmnOPQrsTUVwxyz
# Admin Telegram IDs (comma-separated, no spaces)
ADMIN_IDS=123456789,987654321

# API Configuration
# ================
# Your OpenRouter API key
API_KEY=sk-or-v1-...

# Performance Settings
# ===================
# Maximum concurrent requests to process
MAX_CONCURRENT_REQUESTS=12
# Maximum requests per model
MAX_REQUESTS_PER_MODEL=3
# Maximum requests per user
MAX_REQUESTS_PER_USER=4
# Requests allowed per day per user
REQUESTS_PER_DAY=20
# File uploads allowed per day per user
FILE_UPLOADS_PER_DAY=10
# Cache time-to-live in seconds (24 hours)
CACHE_TTL=86400
# Maximum cache size
MAX_CACHE_SIZE=500
# API timeout in milliseconds (3 minutes)
API_TIMEOUT=180000

# Question Settings
# ================
# Questions to generate per page for documents
QUESTIONS_PER_PAGE=5
# Questions to generate for images
IMAGE_QUESTIONS_COUNT=5

# Extraction Settings
# ==================
# Number of parallel extraction workers
EXTRACTION_PARALLEL_WORKERS=6
# Extraction timeout in milliseconds (20 minutes)
EXTRACTION_TIMEOUT_MS=1200000
# Extraction cache TTL in milliseconds (1 hour)
EXTRACTION_CACHE_TTL=3600000
# Extraction cache size
EXTRACTION_CACHE_SIZE=50

# Debug Settings
# =============
# Enable debug mode (true/false)
DEBUG_MODE=false
# Log level (debug, info, warn, error)
LOG_LEVEL=info

# Optional: Path Configurations
# ============================
# Only needed if these executables are not in your PATH
# TESSERACT_PATH=C:\\Program Files\\Tesseract-OCR\\tesseract.exe
# POPPLER_PATH=C:\\poppler\\bin 