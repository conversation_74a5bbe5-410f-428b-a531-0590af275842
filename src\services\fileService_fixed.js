// Fixed file service implementation with direct method access
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { extractWithPython, isImageFile } = require('../../python_extract');

/**
 * Downloads a file from Telegram using bot.getFile
 */
async function downloadFile(bot, fileId) {
  try {
    console.log('Downloading file with ID: ' + fileId);
    const fileInfo = await bot.getFile(fileId);
    const botToken = process.env.TELEGRAM_BOT_TOKEN;
    const fileUrl = 'https://api.telegram.org/file/bot' + botToken + '/' + fileInfo.file_path;
    const fileExtension = path.extname(fileInfo.file_path).toLowerCase();
    
    // Create uploads directory in project root if it doesn't exist
    const uploadsDir = path.join(__dirname, '../../uploads');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }
    
    // Save in project uploads directory
    const timestamp = Date.now();
    const filePath = path.join(uploadsDir, 'upload_' + timestamp + fileExtension);
    
    const response = await axios({
      method: 'get',
      url: fileUrl,
      responseType: 'arraybuffer'
    });
    
    fs.writeFileSync(filePath, Buffer.from(response.data));
    console.log('Created file in project directory: ' + filePath);
    return filePath;
  } catch (error) {
    console.error('Error downloading file:', error.message);
    throw new Error('Failed to download file: ' + error.message);
  }
}

/**
 * Enhanced extract function with better error handling
 * @param {string} filePath - Path to the document file
 * @returns {Promise<object>} - Object with text, pageCount, and metadata
 */
async function extractTextFromDocument(filePath) {
  try {
    // Get file extension and check for validity
    const fileExtension = path.extname(filePath).toLowerCase();
    
    if (!fs.existsSync(filePath)) {
      console.error(`File does not exist: ${filePath}`);
      throw new Error('File not found or inaccessible');
    }
    
    // Check file size
    const stats = fs.statSync(filePath);
    if (stats.size === 0) {
      console.error(`File is empty: ${filePath}`);
      throw new Error('File is empty');
    }
    
    // Log additional info for images
    if (isImageFile(filePath)) {
      console.log(`Processing image file: ${filePath} (${Math.round(stats.size / 1024)}KB)`);
    } else {
      console.log(`Processing document file: ${filePath} (${Math.round(stats.size / 1024)}KB)`);
    }
    
    // Call Python extraction with the file path
    console.log(`Using Python extraction for ${fileExtension} file`);
    const result = await extractWithPython(filePath);
    
    // Validate the result
    if (!result) {
      console.error('Python extraction returned null or undefined result');
      throw new Error('Extraction failed: empty result');
    }
    
    if (!result.text || result.text.trim().length === 0) {
      console.error(`Empty text extracted from ${fileExtension} document`);
      throw new Error('No text could be extracted from the document');
    }
    
    // Log success info
    console.log(`Successfully extracted ${result.text.length} characters, ${result.pageCount} pages`);
    return result;
  } catch (error) {
    console.error(`Document extraction error: ${error.message}`);
    throw new Error(`Failed to extract text from document: ${error.message}`);
  }
}

module.exports = { downloadFile, extractTextFromDocument };
