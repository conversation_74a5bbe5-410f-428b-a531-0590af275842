// Admin handlers for bot commands
const { isAdmin } = require('../utils/userUtils');
const database = require('../database/database');
const config = require('../config');
const logger = require('../utils/logger');
const fs = require('fs');
const path = require('path');

/**
 * Handle system status command
 * Shows current bot status, usage statistics and system health
 * @param {Object} ctx Telegram context
 */
async function handleSystemStatus(ctx) {
  try {
    const userId = ctx.from.id;
    
    // Check if user is admin
    if (!isAdmin(userId)) {
      if (ctx.callbackQuery) {
        return ctx.answerCbQuery('You do not have permission to view system status.');
      } else {
        return ctx.reply('You do not have permission to use this command.');
      }
    }
    
    // Get system information
    const os = require('os');
    const startTime = process.uptime();
    
    // Format uptime
    const uptimeFormatted = formatUptime(startTime);
    
    // Get memory usage
    const totalMem = Math.round(os.totalmem() / (1024 * 1024));
    const freeMem = Math.round(os.freemem() / (1024 * 1024));
    const usedMem = totalMem - freeMem;
    const memUsagePercent = Math.round((usedMem / totalMem) * 100);
    
    // Get process memory usage
    const processMemory = Math.round(process.memoryUsage().rss / (1024 * 1024));
    
    // Get database stats if available
    let dbSize = 'Unknown';
    let cacheSize = 'Unknown';
    let feedbackCount = 'Unknown';
    
    try {
      const db = database.db();
      
      if (db) {
        // Get database file size
        const dbPath = path.join(__dirname, '../../data/bot.db');
        if (fs.existsSync(dbPath)) {
          const stats = fs.statSync(dbPath);
          dbSize = formatFileSize(stats.size);
        }
        
        // Use promises to ensure we get the values before creating the message
        const getCacheCount = () => {
          return new Promise((resolve) => {
            db.get('SELECT COUNT(*) as count FROM cache', [], (err, row) => {
              if (!err && row) {
                resolve(row.count);
              } else {
                resolve('Unknown');
              }
            });
          });
        };
        
        const getFeedbackCount = () => {
          return new Promise((resolve) => {
            db.get('SELECT COUNT(*) as count FROM feedback', [], (err, row) => {
              if (!err && row) {
                resolve(row.count);
              } else {
                resolve('Unknown');
              }
            });
          });
        };
        
        // Wait for both queries to complete
        [cacheSize, feedbackCount] = await Promise.all([
          getCacheCount(),
          getFeedbackCount()
        ]);
      }
    } catch (dbError) {
      logger.error(`Error getting database stats: ${dbError.message}`);
    }
    
    // Generate a unique refresh timestamp
    const refreshTime = new Date().toLocaleString();
    const uniqueId = Date.now().toString().slice(-5); // Last 5 digits of timestamp
    
    // Create status message
    const message = `
*📊 System Status Report*

*📡 Bot Status:*
- ⏱️ Uptime: ${uptimeFormatted}
- 💾 Bot Memory: ${processMemory} MB
- 💽 Platform: ${os.platform()} (${os.release()})

*🖥️ System Resources:*
- 📏 Memory: ${usedMem}/${totalMem} MB (${memUsagePercent}% used)
- 🧠 CPU Cores: ${os.cpus().length}
- 🌡️ Load Avg: ${os.loadavg().map(x => x.toFixed(2)).join(', ')}

*🗄️ Database:*
- 📁 DB Size: ${dbSize}
- 🔄 Cache Entries: ${cacheSize}
- 📝 Feedback Count: ${feedbackCount}

*Status: 🟢 System Operational*

_Last updated: ${refreshTime} (${uniqueId})_
`;
    
    // Create keyboard
    const keyboard = {
      inline_keyboard: [
        [
          { text: '🔄 Refresh', callback_data: 'admin_view_system_status' },
          { text: '📊 Model Status', callback_data: 'admin_model_status' }
        ],
        [
          { text: '📝 View Feedback', callback_data: 'admin_view_feedback_json' },
          { text: '⬅️ Back to Settings', callback_data: 'admin_back_to_settings' }
        ]
      ]
    };
    
    // Send or edit message based on context
    if (ctx.callbackQuery) {
      await ctx.editMessageText(message, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });
      await ctx.answerCbQuery('System status updated');
    } else {
      await ctx.reply(message, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });
    }
    
    logger.info(`System status viewed by admin ${userId}`);
  } catch (error) {
    logger.error(`Error handling system status: ${error.message}`);
    if (ctx.callbackQuery) {
      await ctx.answerCbQuery('An error occurred while getting system status.');
    } else {
      await ctx.reply('An error occurred while getting system status.');
    }
  }
}

/**
 * Format uptime from seconds to a human-readable string
 * @param {number} seconds Uptime in seconds
 * @returns {string} Formatted uptime string
 */
function formatUptime(seconds) {
  const days = Math.floor(seconds / (3600 * 24));
  const hours = Math.floor((seconds % (3600 * 24)) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  const parts = [];
  if (days > 0) parts.push(`${days}d`);
  if (hours > 0) parts.push(`${hours}h`);
  if (minutes > 0) parts.push(`${minutes}m`);
  if (secs > 0 || parts.length === 0) parts.push(`${secs}s`);
  
  return parts.join(' ');
}

/**
 * Format file size in bytes to a human-readable string
 * @param {number} bytes File size in bytes
 * @returns {string} Formatted file size string
 */
function formatFileSize(bytes) {
  if (bytes < 1024) return `${bytes} B`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(2)} KB`;
  if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
  return `${(bytes / (1024 * 1024 * 1024)).toFixed(2)} GB`;
}

/**
 * Handle broadcast message to all users
 * @param {Object} ctx Telegram context
 */
async function handleBroadcast(ctx) {
  // Only proceed if user is an admin
  if (!isAdmin(ctx.from.id)) {
    return ctx.reply('You do not have permission to use this command.');
  }

  try {
    // TODO: Implement broadcast functionality
    await ctx.reply('Broadcast message feature will be implemented here.');
  } catch (error) {
    logger.error(`Error handling broadcast command: ${error.message}`);
    return ctx.reply('An error occurred during broadcast operation.');
  }
}

/**
 * Handle user management command
 * @param {Object} ctx Telegram context
 */
async function handleUserManagement(ctx) {
  // Only proceed if user is an admin
  if (!isAdmin(ctx.from.id)) {
    return ctx.reply('You do not have permission to use this command.');
  }

  try {
    // TODO: Implement user management functionality
    await ctx.reply('User management feature will be implemented here.');
  } catch (error) {
    logger.error(`Error handling user management command: ${error.message}`);
    return ctx.reply('An error occurred during user management operation.');
  }
}

/**
 * Handle admin settings panel
 * @param {Object} ctx Telegram context
 */
async function handleAdminSettings(ctx) {
  // Only proceed if user is an admin
  if (!isAdmin(ctx.from.id)) {
    return ctx.reply('You do not have permission to use this command.');
  }

  try {
    // Get current settings
    const questionsPerPage = config.questionsPerPage;
    const imageQuestionsCount = config.imageQuestionsCount;
    const fileUploadsPerDay = config.fileUploadsPerDay;
    
    // Create settings message
    const message = `
*Admin Settings Panel*

Current configuration:
- Questions per page: *${questionsPerPage}*
- Image questions count: *${imageQuestionsCount}*
- File uploads per day: *${fileUploadsPerDay}*

Select a setting to change:
    `;
    
    // Create keyboard with settings options
    const keyboard = {
      inline_keyboard: [
        [
          { text: '⚙️ Questions Per Page', callback_data: 'admin_setting_questions' },
          { text: '🖼️ Image Questions Count', callback_data: 'admin_setting_image_questions' }
        ],
        [
          { text: '📁 Files Per Day', callback_data: 'admin_setting_files' },
          { text: '💾 Save Changes to .env', callback_data: 'admin_save_env' }
        ],
        [
          { text: '📝 View User Feedback', callback_data: 'admin_view_feedback_json' },
          { text: '👥 View Users', callback_data: 'admin_view_users_json' }
        ],
        [
          { text: '📊 System Status', callback_data: 'admin_view_system_status' }
        ]
      ]
    };
    
    // Check if we're coming from a callback query (existing message to edit)
    if (ctx.callbackQuery) {
      await ctx.editMessageText(message, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });
      await ctx.answerCbQuery();
    } else {
      // This is a direct command, send a new message
      await ctx.reply(message, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });
    }
  } catch (error) {
    logger.error(`Error displaying admin settings: ${error.message}`);
    if (ctx.callbackQuery) {
      await ctx.answerCbQuery('An error occurred while displaying admin settings.');
    } else {
      await ctx.reply('An error occurred while displaying admin settings.');
    }
  }
}

/**
 * Handle questions per page setting change
 * @param {Object} ctx Telegram context
 */
async function handleQuestionsPerPageSetting(ctx) {
  // Only proceed if user is an admin
  if (!isAdmin(ctx.from.id)) {
    return ctx.answerCbQuery('You do not have permission to change this setting.');
  }
  
  try {
    const currentValue = config.questionsPerPage;
    const message = `
*Questions Per Page Setting*

Current value: *${currentValue}*

This controls how many questions will be generated for each page in uploaded documents.

Choose a new value or type a custom value with # prefix (e.g. *#15*):
    `;
    
    // Create keyboard with options
    const keyboard = {
      inline_keyboard: [
        [
          { text: '1', callback_data: 'admin_set_questions_1' },
          { text: '2', callback_data: 'admin_set_questions_2' },
          { text: '3', callback_data: 'admin_set_questions_3' }
        ],
        [
          { text: '4', callback_data: 'admin_set_questions_4' },
          { text: '5', callback_data: 'admin_set_questions_5' },
          { text: '10', callback_data: 'admin_set_questions_10' }
        ],
        [
          { text: '✏️ Custom Value', callback_data: 'admin_set_questions_custom' }
        ],
        [
          { text: '← Back to Settings', callback_data: 'admin_back_to_settings' }
        ]
      ]
    };
    
    // Edit the current message
    await ctx.editMessageText(message, {
      parse_mode: 'Markdown',
      reply_markup: keyboard
    });
    
    await ctx.answerCbQuery();
  } catch (error) {
    logger.error(`Error handling questions per page setting: ${error.message}`);
    await ctx.answerCbQuery('An error occurred while changing the setting.');
  }
}

/**
 * Handle image questions count setting change
 * @param {Object} ctx Telegram context
 */
async function handleImageQuestionsCountSetting(ctx) {
  // Only proceed if user is an admin
  if (!isAdmin(ctx.from.id)) {
    return ctx.answerCbQuery('You do not have permission to change this setting.');
  }
  
  try {
    const currentValue = config.imageQuestionsCount;
    const message = `
*Image Questions Count Setting*

Current value: *${currentValue}*

This controls how many questions will be generated for image uploads specifically.

Choose a new value or type a custom value with # prefix (e.g. *#10*):
    `;
    
    // Create keyboard with options
    const keyboard = {
      inline_keyboard: [
        [
          { text: '3', callback_data: 'admin_set_image_questions_3' },
          { text: '5', callback_data: 'admin_set_image_questions_5' },
          { text: '7', callback_data: 'admin_set_image_questions_7' }
        ],
        [
          { text: '10', callback_data: 'admin_set_image_questions_10' },
          { text: '15', callback_data: 'admin_set_image_questions_15' },
          { text: '20', callback_data: 'admin_set_image_questions_20' }
        ],
        [
          { text: '✏️ Custom Value', callback_data: 'admin_set_image_questions_custom' }
        ],
        [
          { text: '← Back to Settings', callback_data: 'admin_back_to_settings' }
        ]
      ]
    };
    
    // Edit the current message
    await ctx.editMessageText(message, {
      parse_mode: 'Markdown',
      reply_markup: keyboard
    });
    
    await ctx.answerCbQuery();
  } catch (error) {
    logger.error(`Error handling image questions count setting: ${error.message}`);
    await ctx.answerCbQuery('An error occurred while changing the setting.');
  }
}

/**
 * Handle files per day setting change
 * @param {Object} ctx Telegram context
 */
async function handleFilesPerDaySetting(ctx) {
  // Only proceed if user is an admin
  if (!isAdmin(ctx.from.id)) {
    return ctx.answerCbQuery('You do not have permission to change this setting.');
  }

  try {
    const currentValue = config.fileUploadsPerDay;
    const message = `
*Files Per Day Setting*

Current value: *${currentValue}*

This controls how many files a user can upload per day.

Choose a new value or type a custom value with # prefix (e.g. *#15*):
    `;
    
    // Create keyboard with options
    const keyboard = {
      inline_keyboard: [
        [
          { text: '1', callback_data: 'admin_set_files_1' },
          { text: '3', callback_data: 'admin_set_files_3' },
          { text: '5', callback_data: 'admin_set_files_5' }
        ],
        [
          { text: '10', callback_data: 'admin_set_files_10' },
          { text: '15', callback_data: 'admin_set_files_15' },
          { text: '20', callback_data: 'admin_set_files_20' }
        ],
        [
          { text: '✏️ Custom Value', callback_data: 'admin_set_files_custom' }
        ],
        [
          { text: '← Back to Settings', callback_data: 'admin_back_to_settings' }
        ]
      ]
    };
    
    // Edit the current message
    await ctx.editMessageText(message, {
      parse_mode: 'Markdown',
      reply_markup: keyboard
    });
    
    await ctx.answerCbQuery();
  } catch (error) {
    logger.error(`Error handling files per day setting: ${error.message}`);
    await ctx.answerCbQuery('An error occurred while changing the setting.');
  }
}

/**
 * Handle custom questions per page input request
 * @param {Object} ctx Telegram context
 */
async function handleCustomQuestionsInput(ctx) {
  // Only proceed if user is an admin
  if (!isAdmin(ctx.from.id)) {
    return ctx.answerCbQuery('You do not have permission to change this setting.');
  }

  try {
    const message = `
*Enter Custom Questions Per Page*

Please reply with a number prefixed with # symbol.
For example: *#12* to set 12 questions per page.

Or click Back to return to the preset options.
    `;
    
    // Edit the current message
    await ctx.editMessageText(message, {
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: [
          [
            { text: '← Back', callback_data: 'admin_setting_questions' }
          ]
        ]
      }
    });
    
    // Make sure the session object exists
    ctx.session = ctx.session || {};
    
    // Save the state that we're waiting for custom questions input
    ctx.session.adminState = 'waiting_for_custom_questions';
    
    logger.info(`Set admin state for user ${ctx.from.id} to "waiting_for_custom_questions"`);
    
    await ctx.answerCbQuery();
  } catch (error) {
    logger.error(`Error handling custom questions input: ${error.message}`);
    await ctx.answerCbQuery('An error occurred while processing your request.');
  }
}

/**
 * Handle custom files per day input request
 * @param {Object} ctx Telegram context
 */
async function handleCustomFilesInput(ctx) {
  // Only proceed if user is an admin
  if (!isAdmin(ctx.from.id)) {
    return ctx.answerCbQuery('You do not have permission to change this setting.');
  }

  try {
    const message = `
*Enter Custom Files Per Day*

Please reply with a number prefixed with # symbol.
For example: *#25* to set 25 files per day.

Or click Back to return to the preset options.
    `;
    
    // Edit the current message
    await ctx.editMessageText(message, {
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: [
          [
            { text: '← Back', callback_data: 'admin_setting_files' }
          ]
        ]
      }
    });
    
    // Make sure the session object exists
    ctx.session = ctx.session || {};
    
    // Save the state that we're waiting for custom files input
    ctx.session.adminState = 'waiting_for_custom_files';
    
    logger.info(`Set admin state for user ${ctx.from.id} to "waiting_for_custom_files"`);
    
    await ctx.answerCbQuery();
  } catch (error) {
    logger.error(`Error handling custom files input: ${error.message}`);
    await ctx.answerCbQuery('An error occurred while processing your request.');
  }
}

/**
 * Handle message with custom values (format: #number)
 * @param {Object} ctx Telegram context
 */
async function handleHashNumberMessage(ctx) {
  try {
    // Only proceed if user is an admin
    if (!isAdmin(ctx.from.id)) {
      return false; // Not handled
    }
    
    // Check if the message has the right format
    const text = ctx.message.text;
    if (!text || !text.startsWith('#')) {
      return false; // Not handled
    }
    
    // Extract the number
    const numberStr = text.substring(1);
    const number = parseInt(numberStr, 10);
    
    if (isNaN(number) || number <= 0) {
      await ctx.reply('Please enter a valid positive number prefixed with #. For example: #15');
      return true; // Handled
    }
    
    // Get the admin state
    const session = ctx.session || {};
    logger.info(`Admin ${ctx.from.id} sent custom value: ${number}, session state: ${session.adminState}`);
    
    // Handle based on state
    if (session.adminState === 'waiting_for_custom_questions') {
      // Set questions per page to the custom value
      await setQuestionsPerPage(ctx, number);
      // Clear state
      ctx.session.adminState = null;
      await ctx.reply(`Questions per page set to ${number}.`);
      
      // Recreate the admin settings panel in a new message
      await handleAdminSettings(ctx);
      return true; // Handled
    }
    else if (session.adminState === 'waiting_for_custom_files') {
      // Set files per day to the custom value
      await setFilesPerDay(ctx, number);
      // Clear state
      ctx.session.adminState = null;
      await ctx.reply(`Files per day set to ${number}.`);
      
      // Recreate the admin settings panel in a new message
      await handleAdminSettings(ctx);
      return true; // Handled
    } else {
      // If no state is specified but we got a # message from an admin,
      // ask which setting they want to change
      const customQMsg = await ctx.reply(`Which setting would you like to update with value ${number}?`, {
        reply_markup: {
          inline_keyboard: [
            [
              { text: '⚙️ Questions Per Page', callback_data: `admin_set_questions_${number}` }
            ],
            [
              { text: '📁 Files Per Day', callback_data: `admin_set_files_${number}` }
            ],
            [
              { text: '❌ Cancel', callback_data: 'admin_back_to_settings' }
            ]
          ]
        }
      });
      
      return true; // Handled
    }
  } catch (error) {
    logger.error(`Error handling hash number message: ${error.message}`);
    return false;
  }
}

/**
 * Set questions per page value
 * @param {Object} ctx Telegram context
 * @param {number} value New value
 */
async function setQuestionsPerPage(ctx, value) {
  // Only proceed if user is an admin
  if (!isAdmin(ctx.from.id)) {
    if (ctx.callbackQuery) {
      return ctx.answerCbQuery('You do not have permission to change this setting.');
    } else {
      return ctx.reply('You do not have permission to change this setting.');
    }
  }

  try {
    // Validate the value
    const numValue = parseInt(value, 10);
    
    if (isNaN(numValue) || numValue < 1 || numValue > 100) {
      return ctx.answerCbQuery('Invalid value. Please enter a number between 1 and 100.');
    }
    
    // Update the value in the database
    await database.updateConfig('QUESTIONS_PER_PAGE', numValue.toString());
    logger.info(`Updated QUESTIONS_PER_PAGE in database to ${numValue}`);
    
    // Update the environment variable in-memory
    process.env.QUESTIONS_PER_PAGE = numValue.toString();
    
    // Update config in memory
    config.questionsPerPage = numValue;
    
    // Check if we're coming from a callback or text command
    if (ctx.callbackQuery) {
      await ctx.answerCbQuery(`Questions per page set to ${numValue}`);
      
      // Return to settings panel using the same message
      await handleAdminSettings(ctx);
    }
    // If called from a text handler, don't need to do anything as the caller will handle the response
  } catch (error) {
    logger.error(`Error setting questions per page: ${error.message}`);
    logger.error(error.stack);
    if (ctx.callbackQuery) {
      await ctx.answerCbQuery('An error occurred while updating the setting.');
    } else {
      await ctx.reply('An error occurred while updating the setting.');
    }
  }
}

/**
 * Set files per day value
 * @param {Object} ctx Telegram context
 * @param {number} value New value
 */
async function setFilesPerDay(ctx, value) {
  // Only proceed if user is an admin
  if (!isAdmin(ctx.from.id)) {
    if (ctx.callbackQuery) {
      return ctx.answerCbQuery('You do not have permission to change this setting.');
    } else {
      return ctx.reply('You do not have permission to change this setting.');
    }
  }
  
  try {
    // Validate the value
    const numValue = parseInt(value, 10);
    
    if (isNaN(numValue) || numValue < 1 || numValue > 100) {
      return ctx.answerCbQuery('Invalid value. Please enter a number between 1 and 100.');
    }
    
    // Update the value in the database
    await database.updateConfig('FILE_UPLOADS_PER_DAY', numValue.toString());
    logger.info(`Updated FILE_UPLOADS_PER_DAY in database to ${numValue}`);
    
    // Update the environment variable in-memory
    process.env.FILE_UPLOADS_PER_DAY = numValue.toString();
    
    // Update config in memory
    config.fileUploadsPerDay = numValue;
    
    // Check if we're coming from a callback or text command
    if (ctx.callbackQuery) {
      await ctx.answerCbQuery(`Files per day set to ${numValue}`);
      
      // Return to settings panel using the same message
      await handleAdminSettings(ctx);
    }
    // If called from a text handler, don't need to do anything as the caller will handle the response
  } catch (error) {
    logger.error(`Error setting files per day: ${error.message}`);
    logger.error(error.stack);
    if (ctx.callbackQuery) {
      await ctx.answerCbQuery('An error occurred while updating the setting.');
    } else {
      await ctx.reply('An error occurred while updating the setting.');
    }
  }
}

/**
 * Set the image questions count
 * @param {Object} ctx Telegram context
 * @param {number} value New value for image questions count
 */
async function setImageQuestionsCount(ctx, value) {
  // Only proceed if user is an admin
  if (!isAdmin(ctx.from.id)) {
    return ctx.answerCbQuery('You do not have permission to change this setting.');
  }
  
  try {
    // Validate the value
    const numValue = parseInt(value, 10);
    
    if (isNaN(numValue) || numValue < 1 || numValue > 100) {
      return ctx.answerCbQuery('Invalid value. Please enter a number between 1 and 100.');
    }
    
    // Update the value in the database
    await database.updateConfig('IMAGE_QUESTIONS_COUNT', numValue.toString());
    logger.info(`Updated IMAGE_QUESTIONS_COUNT in database to ${numValue}`);
    
    // Update the environment variable in-memory
    process.env.IMAGE_QUESTIONS_COUNT = numValue.toString();
    
    // Update config in memory
    config.imageQuestionsCount = numValue;
    
    // Show success message and return to settings
    await ctx.answerCbQuery(`Image questions count updated to ${numValue}`);
    
    // Update the settings panel
    await handleAdminSettings(ctx);
  } catch (error) {
    logger.error(`Error setting image questions count: ${error.message}`);
    logger.error(error.stack);
    await ctx.answerCbQuery('An error occurred while setting the value.');
  }
}

/**
 * Save settings to .env file
 * @param {Object} ctx Telegram context
 */
async function saveSettingsToEnv(ctx) {
  // Only proceed if user is an admin
  if (!isAdmin(ctx.from.id)) {
    return ctx.answerCbQuery('You do not have permission to save settings.');
  }
  
  try {
    // Get full path to .env file
    const envPath = path.resolve(__dirname, '../../.env');
    logger.info(`Attempting to save settings to .env file at: ${envPath}`);
    
    if (!fs.existsSync(envPath)) {
      logger.error(`.env file does not exist at ${envPath}`);
      return ctx.answerCbQuery('Error: .env file not found.');
    }
    
    // Try to get the most up-to-date values: first from database, then env vars, then config
    let questionsPerPage, imageQuestionsCount, fileUploadsPerDay;
    
    try {
      // First try to get from database (most accurate)
      const dbQuestionsPerPage = await database.getConfig('QUESTIONS_PER_PAGE');
      const dbImageQuestionsCount = await database.getConfig('IMAGE_QUESTIONS_COUNT');
      const dbFileUploadsPerDay = await database.getConfig('FILE_UPLOADS_PER_DAY');
      
      // The getConfig function returns the value directly
      questionsPerPage = dbQuestionsPerPage || process.env.QUESTIONS_PER_PAGE || config.questionsPerPage.toString();
      imageQuestionsCount = dbImageQuestionsCount || process.env.IMAGE_QUESTIONS_COUNT || config.imageQuestionsCount.toString();
      fileUploadsPerDay = dbFileUploadsPerDay || process.env.FILE_UPLOADS_PER_DAY || config.fileUploadsPerDay.toString();
      
      logger.info(`Retrieved values from database: QUESTIONS_PER_PAGE=${questionsPerPage}, IMAGE_QUESTIONS_COUNT=${imageQuestionsCount}, FILE_UPLOADS_PER_DAY=${fileUploadsPerDay}`);
    } catch (dbError) {
      logger.error(`Error retrieving values from database: ${dbError.message}`);
      // Fallback to environment variables and config
      questionsPerPage = process.env.QUESTIONS_PER_PAGE || config.questionsPerPage.toString();
      imageQuestionsCount = process.env.IMAGE_QUESTIONS_COUNT || config.imageQuestionsCount.toString();
      fileUploadsPerDay = process.env.FILE_UPLOADS_PER_DAY || config.fileUploadsPerDay.toString();
    }
    
    logger.info(`Saving values to .env: QUESTIONS_PER_PAGE=${questionsPerPage}, IMAGE_QUESTIONS_COUNT=${imageQuestionsCount}, FILE_UPLOADS_PER_DAY=${fileUploadsPerDay}`);
    
    // Update environment variables in memory to ensure they match what we're saving
    process.env.QUESTIONS_PER_PAGE = questionsPerPage;
    process.env.IMAGE_QUESTIONS_COUNT = imageQuestionsCount;
    process.env.FILE_UPLOADS_PER_DAY = fileUploadsPerDay;
    
    // Update config in memory as well
    config.questionsPerPage = parseInt(questionsPerPage, 10);
    config.imageQuestionsCount = parseInt(imageQuestionsCount, 10);
    config.fileUploadsPerDay = parseInt(fileUploadsPerDay, 10);
    
    try {
      // Read the current content first
      const currentContent = fs.readFileSync(envPath, 'utf8');
      logger.info(`Successfully read .env file, content length: ${currentContent.length} bytes`);
      
      // Create a backup first
      const backupPath = path.resolve(__dirname, '../../.env.backup');
      fs.writeFileSync(backupPath, currentContent, 'utf8');
      logger.info(`Created backup of .env at ${backupPath}`);
      
      // Process the content line by line to make updates
      let lines = currentContent.split('\n');
      let updated = false;
      
      // Update existing values
      for (let i = 0; i < lines.length; i++) {
        if (lines[i].startsWith('QUESTIONS_PER_PAGE=')) {
          lines[i] = `QUESTIONS_PER_PAGE=${questionsPerPage}`;
          updated = true;
          logger.info(`Updated QUESTIONS_PER_PAGE in .env file to ${questionsPerPage}`);
        }
        else if (lines[i].startsWith('IMAGE_QUESTIONS_COUNT=')) {
          lines[i] = `IMAGE_QUESTIONS_COUNT=${imageQuestionsCount}`;
          updated = true;
          logger.info(`Updated IMAGE_QUESTIONS_COUNT in .env file to ${imageQuestionsCount}`);
        }
        else if (lines[i].startsWith('FILE_UPLOADS_PER_DAY=')) {
          lines[i] = `FILE_UPLOADS_PER_DAY=${fileUploadsPerDay}`;
          updated = true; 
          logger.info(`Updated FILE_UPLOADS_PER_DAY in .env file to ${fileUploadsPerDay}`);
        }
      }
      
      // If values don't exist in the file, add them
      if (!lines.some(line => line.startsWith('QUESTIONS_PER_PAGE='))) {
        lines.push(`QUESTIONS_PER_PAGE=${questionsPerPage}`);
        updated = true;
        logger.info(`Added new QUESTIONS_PER_PAGE to .env file`);
      }
      
      if (!lines.some(line => line.startsWith('IMAGE_QUESTIONS_COUNT='))) {
        lines.push(`IMAGE_QUESTIONS_COUNT=${imageQuestionsCount}`);
        updated = true;
        logger.info(`Added new IMAGE_QUESTIONS_COUNT to .env file`);
      }
      
      if (!lines.some(line => line.startsWith('FILE_UPLOADS_PER_DAY='))) {
        lines.push(`FILE_UPLOADS_PER_DAY=${fileUploadsPerDay}`);
        updated = true;
        logger.info(`Added new FILE_UPLOADS_PER_DAY to .env file`);
      }
      
      if (!updated) {
        logger.info(`No changes needed to .env file`);
      } else {
        logger.info(`Successfully updated all settings in .env file and memory`);
      }
      
      // Write the updated content
      const newContent = lines.join('\n');
      
      // Try to write the file with explicit permissions
      try {
        // Try to force write with a temporary file first
        const tempPath = path.resolve(__dirname, '../../.env.temp');
        fs.writeFileSync(tempPath, newContent, { encoding: 'utf8', mode: 0o666 });
        
        if (fs.existsSync(tempPath)) {
          // Copy temp to actual file
          fs.copyFileSync(tempPath, envPath);
          // Cleanup
          fs.unlinkSync(tempPath);
          logger.info(`Successfully saved .env file using temp file method`);
        } else {
          throw new Error("Failed to create temp file");
        }
      } catch (writeErr) {
        logger.error(`Error writing temp file: ${writeErr.message}`);
        
        // Direct write as fallback
        try {
          fs.writeFileSync(envPath, newContent, 'utf8');
          logger.info(`Successfully saved .env file directly`);
        } catch (directWriteErr) {
          logger.error(`Direct write failed: ${directWriteErr.message}`);
          throw directWriteErr;
        }
      }
      
      // Try to reload environment variables
      try {
        require('dotenv').config();
        logger.info(`Reloaded environment variables from .env file`);
      } catch (dotenvErr) {
        logger.error(`Error reloading environment variables: ${dotenvErr.message}`);
      }
      
      // Show success message
      await ctx.answerCbQuery('Settings saved to .env file successfully');
      
      // Update the settings panel
      await handleAdminSettings(ctx);
    } catch (fileErr) {
      logger.error(`File operation error: ${fileErr.message}`);
      logger.error(fileErr.stack);
      await ctx.answerCbQuery('Error accessing or modifying .env file.');
    }
  } catch (error) {
    logger.error(`Error saving settings to .env: ${error.message}`);
    logger.error(error.stack);
    await ctx.answerCbQuery('An error occurred while saving settings to .env file.');
  }
}

/**
 * Handle logs review command
 * @param {Object} ctx Telegram context
 */
async function handleLogsReview(ctx) {
  // Only proceed if user is an admin
  if (!isAdmin(ctx.from.id)) {
    return ctx.reply('You do not have permission to use this command.');
  }

  try {
    // TODO: Implement logs viewer
    await ctx.reply('Logs review feature will be implemented here.');
  } catch (error) {
    logger.error(`Error handling logs review command: ${error.message}`);
    return ctx.reply('An error occurred during logs review operation.');
  }
}

/**
 * Handle feedback JSON command - sends the feedback JSON file to admin
 * @param {Object} ctx Telegram context
 */
async function handleFeedbackJson(ctx) {
  try {
    const userId = ctx.from.id;
    
    // Check if user is admin
    if (!isAdmin(userId)) {
      return ctx.reply('You do not have permission to use this command.');
    }
    
    const feedbackService = require('../services/feedbackService');
    const feedbackFilePath = path.join(__dirname, '../../data/feedback.json');
    
    // Check if file exists
    if (!fs.existsSync(feedbackFilePath)) {
      return ctx.reply('No feedback data file exists yet.');
    }
    
    // Get some feedback stats
    const stats = await feedbackService.getFeedbackStats();
    
    // Get feedback from JSON
    const feedback = feedbackService.getFeedbackFromJson(false, 100);
    
    if (feedback.length === 0) {
      return ctx.reply('No feedback data found in the JSON file.');
    }
    
    // Send stats message
    await ctx.reply(`📊 *Feedback Stats*\n\n` +
                    `Total feedback: ${stats.total}\n` +
                    `Unread: ${stats.unread}\n` +
                    `👍 Good: ${stats.ratings.good || 0}\n` +
                    `👎 Bad: ${stats.ratings.bad || 0}\n` +
                    `✍️ Opinion: ${stats.ratings.opinion || 0}\n\n` +
                    `Sending feedback JSON file...`, {
      parse_mode: 'Markdown'
    });
    
    // Send the file
    await ctx.replyWithDocument({ 
      source: feedbackFilePath,
      filename: 'user_feedback.json'
    }, {
      caption: 'User feedback data in JSON format'
    });
    
    logger.info(`Feedback JSON file sent to admin ${userId}`);
  } catch (error) {
    logger.error(`Error handling feedback JSON command: ${error.message}`);
    await ctx.reply('An error occurred while retrieving feedback data.');
  }
}

/**
 * Handle feedback viewer with pagination
 * @param {Object} ctx Telegram context
 * @param {number} page Page number (0-based)
 */
async function handleFeedbackJsonViewer(ctx, page = 0) {
  try {
    const userId = ctx.from.id;
    
    // Check if user is admin
    if (!isAdmin(userId)) {
      if (ctx.callbackQuery) {
        await ctx.answerCbQuery('You do not have permission to view feedback data.');
      } else {
        await ctx.reply('You do not have permission to use this command.');
      }
      return;
    }
    
    const feedbackService = require('../services/feedbackService');
    const feedbackFilePath = path.join(__dirname, '../../data/feedback.json');
    
    // Check if file exists
    if (!fs.existsSync(feedbackFilePath)) {
      if (ctx.callbackQuery) {
        await ctx.answerCbQuery('No feedback data file exists yet.');
        await ctx.editMessageText('No feedback data file exists yet.');
      } else {
        await ctx.reply('No feedback data file exists yet.');
      }
      return;
    }
    
    // Get feedback from JSON
    const allFeedback = feedbackService.getFeedbackFromJson(false);
    
    if (allFeedback.length === 0) {
      if (ctx.callbackQuery) {
        await ctx.answerCbQuery('No feedback data found in the JSON file.');
        await ctx.editMessageText('No feedback data found in the JSON file.');
      } else {
        await ctx.reply('No feedback data found in the JSON file.');
      }
      return;
    }
    
    // Calculate pagination
    const itemsPerPage = 5;
    const maxPage = Math.ceil(allFeedback.length / itemsPerPage) - 1;
    
    // Validate page number
    if (page < 0) page = 0;
    if (page > maxPage) page = maxPage;
    
    // Get subset of feedback for current page
    const startIdx = page * itemsPerPage;
    const endIdx = Math.min(startIdx + itemsPerPage, allFeedback.length);
    const feedbackItems = allFeedback.slice(startIdx, endIdx);
    
    // Add a timestamp to force message update when refreshing
    const refreshTime = new Date().toLocaleTimeString();
    const uniqueId = Date.now().toString().slice(-5); // Last 5 digits of timestamp for uniqueness
    
    // Format feedback items
    let messageText = `📝 *User Feedback (JSON)* - Page ${page + 1}/${maxPage + 1}\n\n`;
    
    feedbackItems.forEach((item, index) => {
      const date = new Date(item.timestamp);
      const formattedDate = `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
      
      let ratingEmoji = '✍️';
      if (item.rating === 'good') ratingEmoji = '👍';
      if (item.rating === 'bad') ratingEmoji = '👎';
      
      messageText += `*${startIdx + index + 1}. ${ratingEmoji} ${item.username || 'User'}*\n`;
      messageText += `📝 Rating: ${item.rating}\n`;
      if (item.suggestion) {
        messageText += `💬 Suggestion: "${item.suggestion}"\n`;
      }
      messageText += `📊 Quiz: ${item.quizType || 'Unknown'}, Score: ${item.score || 0}%\n`;
      messageText += `🕒 ${formattedDate}\n`;
      messageText += `📌 ID: ${item.userId}\n`;
      messageText += `${item.isRead ? '✅ Read' : '📬 Unread'}\n\n`;
    });
    
    // Add last updated timestamp at the end
    messageText += `_Last updated: ${refreshTime} (${uniqueId})_`;
    
    // Create pagination keyboard
    const keyboard = {
      inline_keyboard: [
        // Navigation buttons
        [
          { text: '⬅️ Previous', callback_data: `feedback_json_page_${page - 1}`, disabled: page === 0 },
          { text: `${page + 1}/${maxPage + 1}`, callback_data: 'feedback_json_current_page' },
          { text: 'Next ➡️', callback_data: `feedback_json_page_${page + 1}`, disabled: page === maxPage }
        ],
        // Action buttons
        [
          { text: '🔄 Refresh', callback_data: `feedback_json_page_${page}` },
          { text: '📥 Download JSON', callback_data: 'feedback_json_download' },
          { text: '✅ Mark All Read', callback_data: 'feedback_json_mark_all_read' }
        ],
        // Back button
        [
          { text: '⬅️ Back to Admin Settings', callback_data: 'admin_back_to_settings' }
        ]
      ]
    };
    
    // Disable Previous/Next buttons if at first/last page
    if (page === 0) {
      keyboard.inline_keyboard[0][0] = { text: '⬅️ Previous', callback_data: 'feedback_json_no_action' };
    }
    if (page === maxPage) {
      keyboard.inline_keyboard[0][2] = { text: 'Next ➡️', callback_data: 'feedback_json_no_action' };
    }
    
    // Send or edit message based on context
    if (ctx.callbackQuery) {
      try {
        await ctx.editMessageText(messageText, {
          parse_mode: 'Markdown',
          reply_markup: keyboard
        });
        await ctx.answerCbQuery(`Showing feedback page ${page + 1}`);
      } catch (editError) {
        // Handle Telegram's "message not modified" error gracefully
        if (editError.description && editError.description.includes('message is not modified')) {
          await ctx.answerCbQuery('Feedback already up-to-date');
        } else {
          throw editError; // Re-throw if it's a different error
        }
      }
    } else {
      await ctx.reply(messageText, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });
    }
    
    logger.info(`Feedback JSON viewer page ${page + 1} shown to admin ${userId}`);
  } catch (error) {
    logger.error(`Error handling feedback JSON viewer: ${error.message}`);
    if (ctx.callbackQuery) {
      await ctx.answerCbQuery('An error occurred while retrieving feedback data.');
    } else {
      await ctx.reply('An error occurred while retrieving feedback data.');
    }
  }
}

/**
 * Handle model status request
 * Shows the status of AI models used by the bot
 * @param {Object} ctx Telegram context
 */
async function handleModelStatus(ctx) {
  // Only proceed if user is an admin
  if (!isAdmin(ctx.from.id)) {
    return ctx.answerCbQuery('You do not have permission to use this command.');
  }

  try {
    // Try to get actual API information if available
    const apiService = require('../services/apiService');
    
    // Default values
    let apiInfo = {
      apiKeys: '1/1',  // Default to 1/1 for single key
      activeModel: 'Unknown',
      rateLimit: 'Good',
      usageToday: 0,
      avgResponseTime: '0s',
      successRate: '0%',
      cacheHitRate: '0%'
    };
    
    try {
      // Try to get model info if selectModel exists
      if (typeof apiService.selectModel === 'function') {
        const currentModel = apiService.selectModel();
        apiInfo.activeModel = currentModel || 'Default Model';
      }
      
      // Try to get API usage statistics if getApiUsageStats exists
      if (typeof apiService.getApiUsageStats === 'function') {
        const stats = await apiService.getApiUsageStats();
        if (stats) {
          apiInfo.usageToday = stats.totalRequests || 0;
          apiInfo.successRate = `${stats.totalRequests > 0 ? ((stats.successfulRequests / stats.totalRequests) * 100).toFixed(1) : 0}%`;
          apiInfo.avgResponseTime = `${stats.avgResponseTime ? stats.avgResponseTime.toFixed(1) : 0}s`;
        }
      }
    } catch (statsError) {
      logger.debug(`Could not get API stats: ${statsError.message}`);
    }
    
    // Add a timestamp to force message update when refreshing
    const refreshTime = new Date().toLocaleTimeString();
    const uniqueId = Date.now().toString().slice(-5); // Last 5 digits of timestamp for uniqueness
    
    const message = `
*🤖 AI Model Status*

API Status:
- API Keys: ${apiInfo.apiKeys}
- Rate Limits: ${apiInfo.rateLimit}
- Usage Today: ${apiInfo.usageToday} requests

Recent Performance:
- Avg. Response Time: ${apiInfo.avgResponseTime}
- Success Rate: ${apiInfo.successRate}
- Cache Hit Rate: ${apiInfo.cacheHitRate}

*Model Information:*
- Using: ${apiInfo.activeModel}
- Status: 🟢 Active

_Last updated: ${refreshTime} (${uniqueId})_
`;
    
    // Create keyboard
    const keyboard = {
      inline_keyboard: [
        [
          { text: '🔄 Refresh', callback_data: 'admin_model_status' },
          { text: '🧪 Test Model', callback_data: 'admin_test_models' }
        ],
        [
          { text: '⬅️ Back to Admin Settings', callback_data: 'admin_back_to_settings' }
        ]
      ]
    };
    
    if (ctx.callbackQuery) {
      try {
        await ctx.editMessageText(message, {
          parse_mode: 'Markdown',
          reply_markup: keyboard
        });
        await ctx.answerCbQuery('Model status updated');
      } catch (editError) {
        // Handle Telegram's "message not modified" error gracefully
        if (editError.description && editError.description.includes('message is not modified')) {
          await ctx.answerCbQuery('Status already up-to-date');
        } else {
          throw editError; // Re-throw if it's a different error
        }
      }
    } else {
      await ctx.reply(message, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });
    }
  } catch (error) {
    logger.error(`Error handling model status: ${error.message}`);
    if (ctx.callbackQuery) {
      await ctx.answerCbQuery('An error occurred while checking model status.');
    } else {
      await ctx.reply('An error occurred while checking model status.');
    }
  }
}

/**
 * Handle model testing request
 * Tests AI model and returns results
 * @param {Object} ctx Telegram context
 */
async function handleTestModels(ctx) {
  // Only proceed if user is an admin
  if (!isAdmin(ctx.from.id)) {
    return ctx.answerCbQuery('You do not have permission to use this command.');
  }

  try {
    await ctx.answerCbQuery('Starting model test...');
    
    const testMessage = await ctx.reply(`
*🧪 Model Test*

Testing AI model...
Sending test request to API...

_This may take a few seconds..._
`, { parse_mode: 'Markdown' });
    
    // Test the API with a simple request
    const apiService = require('../services/apiService');
    
    const startTime = Date.now();
    let testResults = {
      success: false,
      responseTime: 0,
      error: null,
      sample: null,
      model: 'Unknown'
    };
    
    try {
      // Check if required functions exist
      if (typeof apiService.selectModel !== 'function') {
        throw new Error('Model testing functionality is not available');
      }
      
      if (typeof apiService.testApiConnection !== 'function') {
        throw new Error('API test functionality is not available');
      }
      
      // Try to get the current model name
      testResults.model = apiService.selectModel();
      
      // Send a test request to generate a simple true/false question
      const testPrompt = "Generate one simple true/false question about general knowledge.";
      const response = await apiService.testApiConnection(testPrompt);
      
      testResults.responseTime = Date.now() - startTime;
      testResults.success = true;
      
      // Handle different response formats
      if (response) {
        if (typeof response === 'string') {
          testResults.sample = response.substring(0, 100) + '...';
        } else if (response.text) {
          testResults.sample = response.text.substring(0, 100) + '...';
        } else if (response.choices && response.choices[0] && response.choices[0].message) {
          testResults.sample = response.choices[0].message.content.substring(0, 100) + '...';
        } else {
          testResults.sample = JSON.stringify(response).substring(0, 100) + '...';
        }
        
        // Get model from response if available
        if (response.model) {
          testResults.model = response.model;
        }
      } else {
        testResults.sample = 'No response received';
      }
    } catch (testError) {
      testResults.responseTime = Date.now() - startTime;
      testResults.success = false;
      testResults.error = testError.message || 'Unknown error occurred';
    }
    
    // Format the results message
    const resultsMessage = `
*🧪 Model Test Results*

*Status:* ${testResults.success ? '✅ Success' : '❌ Failed'}
*Response Time:* ${(testResults.responseTime / 1000).toFixed(2)}s
*Model:* ${testResults.model}

${testResults.success 
  ? `*Sample Output:*\n"${testResults.sample}"`
  : `*Error:*\n"${testResults.error}"`
}

${testResults.success 
  ? '*Model is working correctly* ✅'
  : '*Model test failed. Please check API key and connection* ❌'
}
`;
    
    // Create keyboard
    const keyboard = {
      inline_keyboard: [
        [
          { text: '🔄 Run Test Again', callback_data: 'admin_test_models' }
        ],
        [
          { text: '⬅️ Back to Model Status', callback_data: 'admin_model_status' }
        ]
      ]
    };
    
    // Update the message with results
    try {
      await ctx.telegram.editMessageText(
        ctx.chat.id,
        testMessage.message_id,
        undefined,
        resultsMessage,
        {
          parse_mode: 'Markdown',
          reply_markup: keyboard
        }
      );
    } catch (editError) {
      logger.error(`Error updating test results: ${editError.message}`);
      // If editing fails, send a new message
      await ctx.reply(resultsMessage, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });
    }
  } catch (error) {
    logger.error(`Error testing model: ${error.message}`);
    if (ctx.callbackQuery) {
      await ctx.answerCbQuery('An error occurred while testing the model.');
    } else {
      await ctx.reply('An error occurred while testing the model.');
    }
  }
}

/**
 * Handle user viewer with pagination
 * @param {Object} ctx Telegram context
 * @param {number} page Page number (0-based)
 */
async function handleUserJsonViewer(ctx, page = 0) {
  try {
    const userId = ctx.from.id;
    
    // Check if user is admin
    if (!isAdmin(userId)) {
      if (ctx.callbackQuery) {
        await ctx.answerCbQuery('You do not have permission to view user data.');
      } else {
        await ctx.reply('You do not have permission to use this command.');
      }
      return;
    }
    
    const userFilePath = path.join(__dirname, '../../data/users.json');
    
    // Check if file exists
    if (!fs.existsSync(userFilePath)) {
      if (ctx.callbackQuery) {
        await ctx.answerCbQuery('No user data file exists yet.');
        await ctx.editMessageText('No user data file exists yet.');
      } else {
        await ctx.reply('No user data file exists yet.');
      }
      return;
    }
    
    // Read users from JSON file
    let allUsers = [];
    try {
      const fileContent = fs.readFileSync(userFilePath, 'utf8');
      allUsers = JSON.parse(fileContent);
      
      // If it's not an array but an object with users property
      if (!Array.isArray(allUsers) && allUsers.users && Array.isArray(allUsers.users)) {
        allUsers = allUsers.users;
      }
      
      // If it's still not an array, convert object to array
      if (!Array.isArray(allUsers)) {
        allUsers = Object.values(allUsers);
      }
    } catch (error) {
      logger.error(`Error reading users.json: ${error.message}`);
      if (ctx.callbackQuery) {
        await ctx.answerCbQuery('Error reading user data file.');
        await ctx.editMessageText('Error reading user data file.');
      } else {
        await ctx.reply('Error reading user data file.');
      }
      return;
    }
    
    if (allUsers.length === 0) {
      if (ctx.callbackQuery) {
        await ctx.answerCbQuery('No user data found in the JSON file.');
        await ctx.editMessageText('No user data found in the JSON file.');
      } else {
        await ctx.reply('No user data found in the JSON file.');
      }
      return;
    }
    
    // Calculate pagination
    const itemsPerPage = 4; // Reduced items per page to show more details
    const maxPage = Math.ceil(allUsers.length / itemsPerPage) - 1;
    
    // Validate page number
    if (page < 0) page = 0;
    if (page > maxPage) page = maxPage;
    
    // Get subset of users for current page
    const startIdx = page * itemsPerPage;
    const endIdx = Math.min(startIdx + itemsPerPage, allUsers.length);
    const userItems = allUsers.slice(startIdx, endIdx);
    
    // Add a timestamp to force message update when refreshing
    const refreshTime = new Date().toLocaleTimeString();
    const uniqueId = Date.now().toString().slice(-5); // Last 5 digits of timestamp for uniqueness
    
    // Format user items with more detailed information
    let messageText = `👥 *User List* - Page ${page + 1}/${maxPage + 1}\n\n`;
    
    userItems.forEach((user, index) => {
      // Get username or first_name as the primary identifier
      const username = user.username ? `@${user.username}` : (user.first_name || 'Unknown User');
      const fullName = [user.first_name, user.last_name].filter(Boolean).join(' ') || 'Unknown';
      
      // Format join date with detailed information
      let joinDate = "Unknown";
      if (user.joinDate || user.join_date || user.joined_at) {
        const date = new Date(user.joinDate || user.join_date || user.joined_at);
        joinDate = `${date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })}`;
      } else {
        // Try to intelligently find a join date field by looking for properties with "join" or "created" in the name
        for (const key in user) {
          const lowercaseKey = key.toLowerCase();
          if ((lowercaseKey.includes('join') || lowercaseKey.includes('creat') || 
               lowercaseKey.includes('regist') || lowercaseKey.includes('signup')) && 
              user[key] && typeof user[key] !== 'object') {
            try {
              const possibleDate = new Date(user[key]);
              if (!isNaN(possibleDate.getTime())) {
                joinDate = `${possibleDate.toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}`;
                break;
              }
            } catch (e) {
              // Invalid date format, continue checking other fields
            }
          }
        }
      }
      
      // Format last activity with detailed information
      let lastActivity = "Unknown";
      if (user.lastActivity || user.last_activity || user.last_seen || user.lastSeen || user.last_accessed) {
        const date = new Date(user.lastActivity || user.last_activity || user.last_seen || user.lastSeen || user.last_accessed);
        // Always get the latest "now" time for accurate calculations
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
        const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        
        // Format relative time
        let relativeTime = "";
        if (diffDays > 0) {
          relativeTime = `(${diffDays}d ${diffHours}h ago)`;
        } else if (diffHours > 0) {
          relativeTime = `(${diffHours}h ago)`;
        } else {
          const diffMinutes = Math.floor((diffTime % (1000 * 60 * 60)) / (1000 * 60));
          if (diffMinutes > 0) {
            relativeTime = `(${diffMinutes}m ago)`;
          } else {
            // If less than a minute, show seconds
            const diffSeconds = Math.floor((diffTime % (1000 * 60)) / 1000);
            if (diffSeconds < 5) {
              relativeTime = `(just now)`;
            } else {
              relativeTime = `(${diffSeconds}s ago)`;
            }
          }
        }
        
        lastActivity = `${date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })} ${relativeTime}`;
      } else {
        // Try to intelligently find a last activity field by looking for properties with "activity", "seen", "access", or "last" in the name
        for (const key in user) {
          const lowercaseKey = key.toLowerCase();
          if ((lowercaseKey.includes('activ') || lowercaseKey.includes('seen') || 
               lowercaseKey.includes('access') || lowercaseKey.includes('last') ||
               lowercaseKey.includes('login')) && 
              user[key] && typeof user[key] !== 'object' && 
              !lowercaseKey.includes('join') && !lowercaseKey.includes('creat')) {
            try {
              const possibleDate = new Date(user[key]);
              if (!isNaN(possibleDate.getTime())) {
                // Always get the latest "now" time for accurate calculations
                const now = new Date();
                const diffTime = Math.abs(now - possibleDate);
                const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
                const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                
                // Format relative time
                let relativeTime = "";
                if (diffDays > 0) {
                  relativeTime = `(${diffDays}d ${diffHours}h ago)`;
                } else if (diffHours > 0) {
                  relativeTime = `(${diffHours}h ago)`;
                } else {
                  const diffMinutes = Math.floor((diffTime % (1000 * 60 * 60)) / (1000 * 60));
                  if (diffMinutes > 0) {
                    relativeTime = `(${diffMinutes}m ago)`;
                  } else {
                    // If less than a minute, show seconds
                    const diffSeconds = Math.floor((diffTime % (1000 * 60)) / 1000);
                    if (diffSeconds < 5) {
                      relativeTime = `(just now)`;
                    } else {
                      relativeTime = `(${diffSeconds}s ago)`;
                    }
                  }
                }
                
                lastActivity = `${possibleDate.toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })} ${relativeTime}`;
                break;
              }
            } catch (e) {
              // Invalid date format, continue checking other fields
            }
          }
        }
      }
      
      // Get user ID
      const userId = user.telegram_id || user.id || 'Unknown';
      
      // Build user profile message with more details
      messageText += `*${startIdx + index + 1}. ${username}*\n`;
      messageText += `👤 *Name:* ${fullName}\n`;
      messageText += `🆔 *ID:* \`${userId}\`\n`;
      
      if (user.language) {
        messageText += `🌐 *Language:* ${user.language}\n`;
      }
      
      // Add detailed join date
      messageText += `📅 *Joined:* ${joinDate}\n`;
      
      // Add detailed last activity 
      messageText += `⏱️ *Last active:* ${lastActivity}\n`;
      
      // Add quiz stats if available with more details
      if (user.quizStats) {
        const totalQuizzes = user.quizStats.total || 0;
        const avgScore = user.quizStats.avgScore || 0;
        const bestScore = user.quizStats.bestScore || 0;
        
        messageText += `📊 *Quiz Stats:*\n`;
        messageText += `   ∙ Total: ${totalQuizzes}\n`;
        messageText += `   ∙ Avg Score: ${avgScore}%\n`;
        
        if (bestScore > 0) {
          messageText += `   ∙ Best Score: ${bestScore}%\n`;
        }
      }
      
      // Add interaction count if available
      if (user.interactions || user.messageCount) {
        const interactions = user.interactions || user.messageCount || 0;
        messageText += `💬 *Interactions:* ${interactions}\n`;
      }
      
      // Add role/status if available
      if (user.role || user.status) {
        messageText += `🔰 *Role:* ${user.role || user.status}\n`;
      }
      
      messageText += '\n';
    });
    
    // Add last updated timestamp at the end
    messageText += `_Last updated: ${refreshTime} (${uniqueId})_`;
    
    // Create pagination keyboard
    const keyboard = {
      inline_keyboard: [
        // Navigation buttons
        [
          { text: '⬅️ Previous', callback_data: `users_json_page_${page - 1}`, disabled: page === 0 },
          { text: `${page + 1}/${maxPage + 1}`, callback_data: 'users_json_current_page' },
          { text: 'Next ➡️', callback_data: `users_json_page_${page + 1}`, disabled: page === maxPage }
        ],
        // Action buttons
        [
          { text: '🔄 Refresh', callback_data: `users_json_page_${page}` },
          { text: '📥 Download JSON', callback_data: 'users_json_download' }
        ],
        // Back button
        [
          { text: '⬅️ Back to Admin Settings', callback_data: 'admin_back_to_settings' }
        ]
      ]
    };
    
    // Disable Previous/Next buttons if at first/last page
    if (page === 0) {
      keyboard.inline_keyboard[0][0] = { text: '⬅️ Previous', callback_data: 'users_json_no_action' };
    }
    if (page === maxPage) {
      keyboard.inline_keyboard[0][2] = { text: 'Next ➡️', callback_data: 'users_json_no_action' };
    }
    
    // Send or edit message based on context
    if (ctx.callbackQuery) {
      try {
        await ctx.editMessageText(messageText, {
          parse_mode: 'Markdown',
          reply_markup: keyboard
        });
        await ctx.answerCbQuery(`Showing users page ${page + 1}`);
      } catch (editError) {
        // Handle Telegram's "message not modified" error gracefully
        if (editError.description && editError.description.includes('message is not modified')) {
          await ctx.answerCbQuery('User data already up-to-date');
        } else {
          throw editError; // Re-throw if it's a different error
        }
      }
    } else {
      await ctx.reply(messageText, {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      });
    }
    
    logger.info(`User JSON viewer page ${page + 1} shown to admin ${userId}`);
  } catch (error) {
    logger.error(`Error handling user JSON viewer: ${error.message}`);
    if (ctx.callbackQuery) {
      await ctx.answerCbQuery('An error occurred while retrieving user data.');
    } else {
      await ctx.reply('An error occurred while retrieving user data.');
    }
  }
}

/**
 * Handle user JSON download - sends the users JSON file to admin
 * @param {Object} ctx Telegram context
 */
async function handleUserJson(ctx) {
  try {
    const userId = ctx.from.id;
    
    // Check if user is admin
    if (!isAdmin(userId)) {
      return ctx.reply('You do not have permission to use this command.');
    }
    
    const userFilePath = path.join(__dirname, '../../data/users.json');
    
    // Check if file exists
    if (!fs.existsSync(userFilePath)) {
      return ctx.reply('No user data file exists yet.');
    }
    
    try {
      // Read file to get user count
      const fileContent = fs.readFileSync(userFilePath, 'utf8');
      const userData = JSON.parse(fileContent);
      
      // Calculate user count
      let userCount = 0;
      if (Array.isArray(userData)) {
        userCount = userData.length;
      } else if (userData.users && Array.isArray(userData.users)) {
        userCount = userData.users.length;
      } else {
        userCount = Object.keys(userData).length;
      }
      
      // Send stats message
      await ctx.reply(`👥 *User Statistics*\n\n` +
                      `Total users: ${userCount}\n\n` +
                      `Sending users JSON file...`, {
        parse_mode: 'Markdown'
      });
      
      // Send the file
      await ctx.replyWithDocument({ 
        source: userFilePath,
        filename: 'users.json'
      }, {
        caption: 'User data in JSON format'
      });
      
      logger.info(`Users JSON file sent to admin ${userId}`);
    } catch (error) {
      logger.error(`Error processing users.json: ${error.message}`);
      await ctx.reply('Error processing user data file.');
    }
  } catch (error) {
    logger.error(`Error handling user JSON command: ${error.message}`);
    await ctx.reply('An error occurred while retrieving user data.');
  }
}

// Export admin handlers
module.exports = {
  handleSystemStatus,
  handleBroadcast,
  handleUserManagement,
  handleAdminSettings,
  handleQuestionsPerPageSetting,
  handleImageQuestionsCountSetting,
  handleFilesPerDaySetting,
  handleCustomQuestionsInput,
  handleCustomFilesInput,
  handleHashNumberMessage,
  setQuestionsPerPage,
  setImageQuestionsCount,
  setFilesPerDay,
  saveSettingsToEnv,
  handleLogsReview,
  handleFeedbackJson,
  handleFeedbackJsonViewer,
  handleModelStatus,
  handleTestModels,
  handleUserJsonViewer,
  handleUserJson
}; 