// API related functions
const axios = require('axios');
const database = require('../database/database');
const fs = require('fs');
const path = require('path');
const config = require('../config');
const modelManager = require('./modelManager');
const logger = require('../utils/logger');

// Variable to track the current model index for round-robin
let currentModelIndex = 0;

// Using OpenRouter API instead of getimg.ai which is giving 404 errors
const API_URL = "https://openrouter.ai/api/v1/chat/completions";

/**
 * Get the API key
 * @returns {Promise<string|null>} The API key or null if none available
 */
async function getNextApiKey() {
  try {
    // Check if database is initialized
    const db = database.db();
    
    if (!db) {
      console.log('Database not initialized, using environment key');
      return config.apiKey || null;
    }
    
    // First try to get keys from the database
    return new Promise((resolve, reject) => {
      // Check if the api_keys table exists
      db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='api_keys'", [], (err, row) => {
        if (err || !row) {
          console.log('API keys table not found, using environment key');
          resolve(config.apiKey || null);
          return;
        }
        
        // Table exists, query keys
        db.all('SELECT id, key, is_active FROM api_keys WHERE is_active = 1 LIMIT 1', [], async (err, rows) => {
          if (err) {
            console.error('Error fetching API keys:', err);
            resolve(config.apiKey || null);
            return;
          }
          
          // If no keys in database, check environment variables
          if (!rows || rows.length === 0) {
            console.log('No active API keys in database, using environment key');
            resolve(config.apiKey || null);
            return;
          }
            
            // Update last used timestamp for this key
            db.run('UPDATE api_keys SET last_used = ? WHERE id = ?', 
            [Date.now(), rows[0].id], (err) => {
                if (err) console.error('Error updating key usage timestamp:', err);
              });
            
          console.log('Using database API key');
          resolve(rows[0].key);
        });
      });
    });
  } catch (error) {
    console.error('Error in getNextApiKey:', error);
    return config.apiKey || null;
  }
}

/**
 * Get the current API key info
 * @returns {Object} The current key info
 */
function getCurrentKeyIndices() {
  return {
    environmentKeyAvailable: !!config.apiKey
  };
}

/**
 * Add an API key to the database
 * @param {string} apiKey The API key to add
 * @returns {Promise<boolean>} Success
 */
async function addApiKey(apiKey) {
  const db = database.db();
  
  return new Promise((resolve, reject) => {
    // Check if key already exists
    db.get('SELECT id FROM api_keys WHERE key = ?', [apiKey], (err, row) => {
      if (err) {
        console.error('Error checking for existing key:', err);
        reject(err);
        return;
      }
      
      if (row) {
        console.log('API key already exists in database');
        resolve(false);
        return;
      }
      
      // Insert new key
      db.run('INSERT INTO api_keys (key, is_active, last_used) VALUES (?, 1, ?)', 
        [apiKey, Date.now()], function(err) {
          if (err) {
            console.error('Error adding API key:', err);
            reject(err);
            return;
          }
          
          console.log(`Added new API key with ID ${this.lastID}`);
          resolve(true);
        });
    });
  });
}

/**
 * Verify all API keys and return stats
 * @returns {Promise<Object>} Object with total, valid, and invalid counts
 */
async function verifyApiKeys() {
  const stats = {
    total: 0,
    valid: 0,
    invalid: 0,
    databaseKeys: { total: 0, valid: 0, invalid: 0 },
    environmentKeys: { total: 0, valid: 0, invalid: 0 }
  };
  
  try {
    const db = database.db();
    
    // First verify database keys
    await new Promise((resolve, reject) => {
      db.all('SELECT id, key FROM api_keys', [], async (err, rows) => {
        if (err) {
          console.error('Error fetching API keys for verification:', err);
          resolve(); // Continue with environment keys
          return;
        }
        
        if (rows && rows.length > 0) {
          stats.total += rows.length;
          stats.databaseKeys.total = rows.length;
          
          // Test each key
          for (const row of rows) {
            try {
              const isValid = await testApiKey(row.key);
              if (isValid) {
                stats.valid++;
                stats.databaseKeys.valid++;
                // Update key status if needed
                db.run('UPDATE api_keys SET is_active = 1 WHERE id = ?', [row.id]);
              } else {
                stats.invalid++;
                stats.databaseKeys.invalid++;
                // Mark key as inactive
                db.run('UPDATE api_keys SET is_active = 0 WHERE id = ?', [row.id]);
              }
            } catch (error) {
              console.error(`Error testing API key ${row.id}:`, error);
              stats.invalid++;
              stats.databaseKeys.invalid++;
              // Mark key as inactive
              db.run('UPDATE api_keys SET is_active = 0 WHERE id = ?', [row.id]);
            }
          }
        }
        
        resolve();
      });
    });
    
    // Now check environment variable keys
    if (config.apiKeys.length > 0) {
      stats.total += config.apiKeys.length;
      stats.environmentKeys.total = config.apiKeys.length;
      
      // Test each key
      for (const key of config.apiKeys) {
        try {
          const isValid = await testApiKey(key);
          if (isValid) {
            stats.valid++;
            stats.environmentKeys.valid++;
          } else {
            stats.invalid++;
            stats.environmentKeys.invalid++;
          }
        } catch (error) {
          console.error(`Error testing API key from environment:`, error);
          stats.invalid++;
          stats.environmentKeys.invalid++;
        }
      }
    }
    
    return stats;
  } catch (error) {
    console.error('Error in verifyApiKeys:', error);
    return stats;
  }
}

/**
 * Test if an API key is valid
 * @param {string} apiKey - API key to test
 * @returns {Promise<boolean>} Whether the key is valid
 */
async function testApiKey(apiKey) {
  try {
    // Make a minimal API request to check if the key works
    const response = await axios.post(API_URL, {
      model: 'openai/gpt-3.5-turbo-instruct:free',
      messages: [{ role: 'user', content: 'Hi' }],
      max_tokens: 5
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': 'https://telegram-mcq-tf-bot.com',
        'X-Title': 'Telegram MCQ/TF Question Generator'
      },
      timeout: 10000 // 10s timeout for key testing
    });
    
    return response.status === 200;
  } catch (error) {
    logger.error(`API key test failed: ${error.message}`);
    return false;
  }
}

/**
 * Get available models from config
 * @returns {Array} Array of model strings
 */
function getModels() {
  return config.models;
}

/**
 * Select a model using round-robin strategy
 * @returns {string} Selected model
 */
function selectModel() {
  const models = getModels();
  
  // If no models configured, return default
  if (!models || models.length === 0) {
    logger.warn('No models configured, using default model');
    return 'openai/gpt-3.5-turbo-instruct:free';
  }
  
  // Get the next model in rotation
  const selectedModel = models[currentModelIndex];
  
  // Update the index for next time (wrapping around at the end)
  currentModelIndex = (currentModelIndex + 1) % models.length;
  
  logger.api(`Selected model: ${selectedModel}`);
  return selectedModel;
}

/**
 * Record model usage statistics in the database
 * @param {string} model Model name
 * @param {boolean} success Whether the request was successful
 * @param {number} duration Time taken for the request in ms
 */
function recordModelStats(model, success, duration) {
  try {
    // Get database instance from the database module
    const db = database.db();
    
    if (!db) {
      logger.warn('Database not initialized for model stats');
      return;
    }
    
    // Ensure we have access to run function
    if (typeof db.run !== 'function') {
      logger.error('Database instance does not have run method');
      return;
    }
    
    const timestamp = Date.now();
    
    // Insert a new record
    db.run(
      'INSERT INTO model_stats (model, success, duration, timestamp) VALUES (?, ?, ?, ?)',
      [model, success ? 1 : 0, duration, timestamp],
      function(err) {
        if (err) {
          logger.error(`Error recording model stats: ${err.message}`);
        } else {
          logger.debug(`Recorded stats for model ${model}: success=${success}, duration=${duration}ms`);
        }
      }
    );
  } catch (error) {
    logger.error(`Error in recordModelStats: ${error.message}`);
  }
}

/**
 * Generate questions using the specified AI model
 * @param {string} text Text to generate questions from
 * @param {string} type Question type ('MCQ' or 'TF')
 * @param {number} count Number of questions to generate
 * @returns {Promise<Array>} Array of question objects
 */
async function generateQuestionsFromAPI(text, type, count) {
  // Use round-robin model selection
  const model = selectModel();
  const startTime = Date.now();
  
  // Create system prompt based on question type
  let systemPrompt = '';
  let userPrompt = '';
  
  if (type === 'MCQ') {
    systemPrompt = 'You are an expert educator specialized in creating multiple-choice questions. Your output must follow the exact format specified.';
    userPrompt = 
      `Create ${count} multiple-choice (MCQ) questions based on the following text. 
      Each question should have exactly 4 options labeled A, B, C, and D.

      IMPORTANT: You MUST format your response as a valid JSON array with this exact structure:
      [
        {
          "question": "Question text",
          "options": ["Option A", "Option B", "Option C", "Option D"],
          "answer": "A", 
          "explanation": "Brief explanation of the answer"
        }
      ]
      
      If you cannot output valid JSON, use this text format instead:
      1. Question text
      A) Option A
      B) Option B
      C) Option C
      D) Option D
      Answer: A
      Explanation: Brief explanation
      
      2. Second question...
      
      Text: ${text.substring(0, 12000)}`;
      } else {
    systemPrompt = 'You are an expert educator specialized in creating true/false questions. Your output must follow the exact format specified.';
    userPrompt = 
      `Create ${count} true/false questions based on the following text.

      IMPORTANT: You MUST format your response as a valid JSON array with this exact structure:
      [
        {
          "question": "Question text", 
          "answer": true,
          "explanation": "Brief explanation of why the statement is true or false"
        }
      ]
      
      If you cannot output valid JSON, use this text format instead:
      1. Question text
      Answer: True
      Explanation: Brief explanation
      
      2. Second question...
      
      Text: ${text.substring(0, 12000)}`;
  }

  const messages = [
    { "role": "system", "content": systemPrompt },
    { "role": "user", "content": userPrompt }
  ];
  
  try {
    logger.api(`Sending request to model ${model}`);
    
    // Prepare request payload
    const payload = {
      model: model,
      messages: messages,
      temperature: 0.7,
      max_tokens: 2048
    };
    
    // Only add response_format for models that support it
    // Some models like Llama/Gemma may not support the response_format parameter
    if (model.includes('gpt') || model.includes('gemini')) {
      payload.response_format = { type: "json_object" };
    }
    
    const response = await axios.post(API_URL, payload, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`,
        'HTTP-Referer': 'https://telegram-mcq-tf-bot.com',
        'X-Title': 'Telegram MCQ/TF Question Generator'
      },
      timeout: config.requestTimeout || 60000, // Default 60s timeout
    });
    
    const duration = Date.now() - startTime;
    logger.api(`Response received from ${model} in ${duration}ms`);
    
    // Record successful stats
    recordModelStats(model, true, duration);
    
    // Extract content from response - more robustly, handling potentially malformed responses
    let responseContent = '';
    try {
      if (response.data && response.data.choices && 
          response.data.choices.length > 0 && 
          response.data.choices[0].message &&
          response.data.choices[0].message.content) {
        responseContent = response.data.choices[0].message.content;
      } else {
        // Try alternative response formats
        if (response.data && response.data.message) {
          responseContent = response.data.message;
        } else if (response.data && typeof response.data.text === 'string') {
          responseContent = response.data.text;
        } else if (response.data && typeof response.data === 'string') {
          responseContent = response.data;
        } else {
          logger.error('Unexpected API response format');
          logger.debug(`Response structure: ${JSON.stringify(Object.keys(response.data || {}))}`);
          throw new Error('Unexpected API response format');
        }
      }
    } catch (structureError) {
      logger.error(`Error extracting content from response: ${structureError.message}`);
      recordModelStats(model, false, duration);
      return [];
    }
    
    // If we have content, try to parse it
    if (!responseContent) {
      logger.error('Empty response from API');
      recordModelStats(model, false, duration);
      return [];
    }
    
    try {
      // First attempt: try to parse the response as JSON
      try {
        const parsedResponse = JSON.parse(responseContent);
        
        // Check if we got an array directly or need to extract it
        let questions;
        if (Array.isArray(parsedResponse)) {
          questions = parsedResponse;
        } else if (parsedResponse.questions && Array.isArray(parsedResponse.questions)) {
          questions = parsedResponse.questions;
        } else {
          // Look for any array property that might contain questions
          const arrayProps = Object.keys(parsedResponse).filter(key => 
            Array.isArray(parsedResponse[key]) && 
            parsedResponse[key].length > 0 &&
            parsedResponse[key][0].question
          );
          
          if (arrayProps.length > 0) {
            questions = parsedResponse[arrayProps[0]];
          } else {
            throw new Error("Couldn't find questions array in JSON response");
          }
        }
        
        // Successfully parsed JSON
        logger.debug("Successfully parsed JSON response from API");
        
        // Validate and post-process questions
        return questions.filter(q => q && q.question).map(q => {
          // Make sure MCQ questions have the correct structure
          if (type === 'MCQ' && (!q.options || !Array.isArray(q.options))) {
            q.options = ["Option A", "Option B", "Option C", "Option D"];
            q.answer = "A";
          }
          return q;
        });
      } catch (jsonError) {
        // Second attempt: Try to extract a JSON array from text with explanatory content
        const jsonArrayMatch = responseContent.match(/(\[\s*\{\s*"question"[\s\S]*\}\s*\])/);
        if (jsonArrayMatch && jsonArrayMatch[1]) {
          try {
            const extractedJson = jsonArrayMatch[1];
            logger.debug("Found JSON array in text response, attempting to parse it");
            const parsedQuestions = JSON.parse(extractedJson);
            
            if (Array.isArray(parsedQuestions) && parsedQuestions.length > 0) {
              logger.info(`Successfully extracted ${parsedQuestions.length} questions from embedded JSON array`);
              recordModelStats(model, true, duration);
              
              // Validate and post-process
              return parsedQuestions.filter(q => q && q.question).map(q => {
                // Make sure MCQ questions have the correct structure
                if (type === 'MCQ' && (!q.options || !Array.isArray(q.options))) {
                  q.options = ["Option A", "Option B", "Option C", "Option D"];
                  q.answer = "A";
                }
                return q;
              });
            }
          } catch (extractedJsonError) {
            logger.warn(`Failed to parse extracted JSON array: ${extractedJsonError.message}`);
          }
        }
        
        // Third attempt: If not valid JSON, try to extract questions from text
        logger.warn(`Response is not valid JSON: ${jsonError.message}. Attempting to parse as text.`);
        
        // Log the first part of the response to help with debugging
        logger.debug(`Response starts with: "${responseContent.substring(0, 100).replace(/\n/g, ' ')}..."`);
        
        // Parse text-based response into structured questions
        const questions = parseTextResponse(responseContent, type);
        
        if (questions.length > 0) {
          logger.info(`Successfully extracted ${questions.length} questions from text response`);
          recordModelStats(model, true, duration);
          return questions;
        } else {
          logger.error(`Failed to extract questions from text response. First 200 chars of response: "${responseContent.substring(0, 200).replace(/\n/g, ' ')}..."`);
          throw new Error("Could not extract valid questions from text response");
        }
      }
    } catch (parseError) {
      logger.error(`Error processing API response: ${parseError.message}`);
      // Log more details about the response for debugging
      if (responseContent) {
        logger.debug(`Response content type: ${typeof responseContent}`);
        logger.debug(`Response length: ${responseContent.length}`);
        logger.debug(`Response preview: ${responseContent.substring(0, 150).replace(/\n/g, ' ')}...`);
      }
      recordModelStats(model, false, duration);
      return [];
    }
  } catch (error) {
    const duration = Date.now() - startTime;
    logger.error(`API request failed: ${error.message}`);
    
    // Record failed stats
    recordModelStats(model, false, duration);
    
    // Re-throw the error for the caller to handle
    throw error;
  }
}

/**
 * Update model statistics in the database
 * @param {string} model - Model name
 * @param {boolean} success - Whether the request was successful
 * @param {string|number} status - HTTP status or error type for failed requests
 */
function updateModelStats(model, success, status = '') {
  try {
    // Get database instance from the database module
    const db = database.db();
    
    if (!db) {
      logger.warn('Database not initialized for model stats');
      return;
    }
    
    // Ensure we have access to run function
    if (typeof db.run !== 'function') {
      logger.error('Database instance does not have run method');
      return;
    }
    
    const timestamp = Date.now();
    
    // Insert a new record
    db.run(
      'INSERT INTO model_stats (model, success, status, timestamp) VALUES (?, ?, ?, ?)',
      [model, success ? 1 : 0, status.toString(), timestamp],
      function(err) {
        if (err) {
          logger.error(`Error updating model stats: ${err.message}`);
        } else {
          logger.debug(`Recorded stats for model ${model}: success=${success}, status=${status}`);
        }
      }
    );
  } catch (error) {
    logger.error(`Error in updateModelStats: ${error.message}`);
  }
}

/**
 * Parse text-based response into structured question objects
 * @param {string} text - Raw text response from API
 * @param {string} type - Question type (MCQ or TF)
 * @returns {Array} Array of structured question objects
 */
function parseTextResponse(text, type) {
  const questions = [];
  
  // First look for any valid JSON data in the response, even if surrounded by explanatory text
  let jsonMatches = text.match(/(\[\s*\{\s*"question"[\s\S]*\}\s*\])/);
  if (jsonMatches && jsonMatches[1]) {
    try {
      const jsonText = jsonMatches[1];
      logger.debug("Found JSON data in text response, attempting to parse it");
      const parsedQuestions = JSON.parse(jsonText);
      if (Array.isArray(parsedQuestions) && parsedQuestions.length > 0) {
        logger.info(`Successfully extracted ${parsedQuestions.length} questions from embedded JSON`);
        return parsedQuestions;
      }
    } catch (e) {
      logger.warn(`Failed to parse embedded JSON: ${e.message}`);
    }
  }
  
  // Next, handle code-formatted JSON in Gemma/Llama responses
  // They often put JSON inside ``` or ```json blocks
  const codeBlockMatch = text.match(/```(?:json)?\s*(\[\s*\{.+?\}\s*\])\s*```/s);
  if (codeBlockMatch) {
    try {
      const extractedJson = codeBlockMatch[1];
      logger.debug("Found JSON code block, attempting to parse it");
      const parsedQuestions = JSON.parse(extractedJson);
      if (Array.isArray(parsedQuestions) && parsedQuestions.length > 0) {
        logger.info(`Successfully extracted ${parsedQuestions.length} questions from code block`);
        return parsedQuestions;
      }
    } catch (e) {
      logger.warn(`Failed to parse JSON from code block: ${e.message}`);
    }
  }
  
  // If we're here, we need to try parsing as plain text
  // Log the first part of the text to help with debugging
  logger.debug(`Parsing text response (first 100 chars): "${text.substring(0, 100).replace(/\n/g, ' ')}..."`);
  
  // Special case for Llama models that start with common introduction phrases
  const hasIntroPhrase = /^(Here are|I've created|I have created|These are|I'll create|I'll generate|I've generated)/i.test(text);
  
  if (hasIntroPhrase && type === 'TF') {
    // This is a Llama model response with True/False questions
    const lines = text.split('\n');
    let currentQuestion = null;
    let currentAnswer = null;
    let currentExplanation = '';
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Skip empty lines
      if (!line) continue;
      
      // Skip introduction lines
      if (i < 3 && /^(Here are|I've created|I have created|These are|I'll create|I'll generate|I've generated)/i.test(line)) {
        continue;
      }
      
      // Check for question pattern: "1. Question text", "Question 1: text", etc.
      const questionMatch = line.match(/^(\d+)[\.\)\:]\s+(.+)$/) || line.match(/^Question\s+(\d+)[\.\)\:]\s*(.+)$/i);
      if (questionMatch) {
        // If we have a previous question complete, add it
        if (currentQuestion) {
          questions.push({
            question: currentQuestion,
            answer: currentAnswer !== null ? currentAnswer : true, // Default to true
            explanation: currentExplanation.trim()
          });
        }
        
        // Start new question
        currentQuestion = questionMatch[2].trim();
        currentAnswer = null;
        currentExplanation = '';
        continue;
      }
      
      // Check for answer pattern with more variations
      if (line.match(/^Answer:?\s*True$/i) || 
          line.match(/^The answer is\s*True$/i) || 
          line.match(/^True[\.]*$/i)) {
        currentAnswer = true;
        continue;
      }
      
      if (line.match(/^Answer:?\s*False$/i) || 
          line.match(/^The answer is\s*False$/i) || 
          line.match(/^False[\.]*$/i)) {
        currentAnswer = false;
        continue;
      }
      
      // Check for explanation pattern with more variations
      if (line.match(/^Explanation:?\s+/i) || line.match(/^Reason:?\s+/i)) {
        currentExplanation = line.replace(/^(Explanation|Reason):?\s+/i, '').trim();
        continue;
      }
      
      // Everything else is potentially explanation
      if (currentQuestion && !line.startsWith("Question") && currentExplanation.length < 500) {
        currentExplanation += line + ' ';
      }
    }
    
    // Add the last question if we have one
    if (currentQuestion) {
      questions.push({
        question: currentQuestion,
        answer: currentAnswer !== null ? currentAnswer : true,
        explanation: currentExplanation.trim()
      });
    }
    
    if (questions.length > 0) {
      logger.info(`Successfully extracted ${questions.length} questions using enhanced Llama TF format`);
      return questions;
    }
  }
  
  // Special case for Llama models with MCQ questions
  if (hasIntroPhrase && type === 'MCQ') {
    // This is a Llama model response with MCQ questions
    const lines = text.split('\n');
    let currentQuestion = null;
    let currentOptions = [];
    let currentAnswer = null;
    let currentExplanation = '';
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Skip empty lines
      if (!line) continue;
      
      // Skip introduction lines
      if (i < 3 && /^(Here are|I've created|I have created|These are|I'll create|I'll generate|I've generated)/i.test(line)) {
        continue;
      }
      
      // Check for question pattern with more variations
      const questionMatch = line.match(/^(\d+)[\.\)\:]\s+(.+)$/) || line.match(/^Question\s+(\d+)[\.\)\:]\s*(.+)$/i);
      if (questionMatch) {
        // If we have a previous question complete, add it
        if (currentQuestion && currentOptions.length >= 2) {
          questions.push({
            question: currentQuestion,
            options: currentOptions.length === 4 ? currentOptions : [...currentOptions, ...Array(4 - currentOptions.length).fill('')].slice(0, 4),
            answer: currentAnswer || 'A', // Default to A
            explanation: currentExplanation.trim()
          });
        }
        
        // Start new question
        currentQuestion = questionMatch[2].trim();
        currentOptions = [];
        currentAnswer = null;
        currentExplanation = '';
        continue;
      }
      
      // Check for option pattern with more variations "A. Option text" or "A) Option text" or "A - Option text"
      const optionMatch = line.match(/^([A-D])[\.\)\:\-]\s+(.+)$/);
      if (optionMatch) {
        const optionLetter = optionMatch[1];
        const optionText = optionMatch[2].trim();
        
        // Add to options array at the correct index
        const index = optionLetter.charCodeAt(0) - 'A'.charCodeAt(0);
        currentOptions[index] = optionText;
        continue;
      }
      
      // Check for answer pattern with more variations
      const answerMatch = line.match(/^Answer:?\s*([A-D])/i) || 
                         line.match(/^The (correct |right )?answer is\s*([A-D])/i) ||
                         line.match(/^Correct (option|answer):?\s*([A-D])/i);
      if (answerMatch) {
        currentAnswer = (answerMatch[1] || answerMatch[2]).toUpperCase();
        continue;
      }
      
      // Check for explanation pattern with more variations
      if (line.match(/^Explanation:?\s+/i) || line.match(/^Reason:?\s+/i)) {
        currentExplanation = line.replace(/^(Explanation|Reason):?\s+/i, '').trim();
        continue;
      }
      
      // Add to explanation if we're past the answer
      if (currentAnswer && currentExplanation.length < 500) {
        currentExplanation += ' ' + line;
      }
    }
    
    // Add the last question if we have one
    if (currentQuestion && currentOptions.length >= 2) {
      questions.push({
        question: currentQuestion,
        options: currentOptions.length === 4 ? currentOptions : [...currentOptions, ...Array(4 - currentOptions.length).fill('')].slice(0, 4),
        answer: currentAnswer || 'A',
        explanation: currentExplanation.trim()
      });
    }
    
    if (questions.length > 0) {
      logger.info(`Successfully extracted ${questions.length} questions using enhanced Llama MCQ format`);
      return questions;
    }
  }
  
  // Remove any introduction text like "Here are the questions..."
  text = text.replace(/^(Here are|I've created|I have created|These are).*?(questions|MCQs|true\/false).*?\n/i, '');
  
  // Try the general approach if the special cases didn't work
  if (type === 'MCQ') {
    // Try multiple patterns to split text into questions
    let questionBlocks = [];
    
    // Pattern 1: Split by numbered questions - the most common format
    questionBlocks = text.split(/(?=^\s*\d+\s*[\.\)\:]\s+)/m).filter(Boolean);
    
    // If we don't have enough blocks, try other patterns
    if (questionBlocks.length < 3) {
      questionBlocks = text.split(/(?=Question\s*\d*\s*[:\.]\s+)/i).filter(Boolean);
    }
    
    if (questionBlocks.length < 3) {
      // Try to split by double newlines and then look for patterns
      const chunks = text.split(/\n\s*\n/).filter(Boolean);
      questionBlocks = [];
      
      for (const chunk of chunks) {
        // More flexible pattern matching for questions
        if (chunk.match(/^\s*\d+[\.\)\:]\s+/) || 
            chunk.match(/^Question/i) || 
            chunk.match(/^[A-D][\.\)\:]/) ||
            chunk.match(/answer/i)) {
          questionBlocks.push(chunk);
        }
      }
    }
    
    // Last resort: split by double newlines if we still don't have blocks
    if (questionBlocks.length < 2) {
      questionBlocks = text.split(/\n\s*\n/).filter(Boolean);
    }
    
    logger.debug(`Found ${questionBlocks.length} potential MCQ question blocks`);
    
    for (const block of questionBlocks) {
      try {
        // Skip if too short or likely not a question
        if (block.length < 20 || block.toLowerCase().includes("here are")) continue;
        
        // Extract question text - multiple patterns
        let questionText = '';
        
        // Try to match different question patterns
        const questionMatch = block.match(/^\s*(?:\d+[\.\)\:]\s+|Question\s*\d*\s*[:\.]\s+)(.+?)(?=\n|$)/m) || 
                             block.match(/^(.+?)(?=\n[A-D][\.\)\:]|$)/m);
                             
        if (questionMatch) {
          questionText = questionMatch[1].trim();
        } else {
          // Just use the first line
          questionText = block.split('\n')[0].trim();
        }
        
        // Skip if looks like an introduction or too short
        if (questionText.length < 5 || 
            questionText.toLowerCase().includes("here are") || 
            questionText.toLowerCase().includes("i've created")) continue;
        
        // Extract options
        const options = [];
        
        // Try multiple patterns for options
        // Pattern 1: A) Option text
        const optionMatches1 = [...block.matchAll(/^[A-D][\)\.\s:]+(.+?)(?=\n[A-D][\)\.\s:]|$|\n\n)/gm)];
        if (optionMatches1.length >= 2) {
          for (const match of optionMatches1) {
            options.push(match[1].trim());
          }
        } else {
          // Pattern 2: lines that look like options after the question
          const lines = block.split('\n').map(l => l.trim()).filter(Boolean);
          let foundOptions = false;
          
          for (let i = 1; i < lines.length; i++) { // Start at 1 to skip the question
            const line = lines[i];
            const optionMatch = line.match(/^([A-D])[\)\.\s:]+(.+)$/);
            
            if (optionMatch) {
              foundOptions = true;
              const index = optionMatch[1].charCodeAt(0) - 'A'.charCodeAt(0);
              options[index] = optionMatch[2].trim();
            } else if (foundOptions && line.toLowerCase().includes('answer')) {
              break; // Stop at the answer line
            }
          }
        }
        
        // Skip if we don't have enough options
        if (options.filter(Boolean).length < 2) continue;
        
        // Make sure we have a complete set of options (fill gaps)
        const filledOptions = [];
        for (let i = 0; i < 4; i++) {
          filledOptions[i] = options[i] || `Option ${String.fromCharCode(65 + i)}`;
        }
        
        // Extract the answer
        let answer = 'A'; // Default
        
        // Try multiple patterns
        const answerMatch1 = block.match(/(?:answer|correct)(?:\s+is|:)\s+([A-D])/i);
        const answerMatch2 = block.match(/^answer:\s*([A-D])/im);
        
        if (answerMatch1) {
          answer = answerMatch1[1].toUpperCase();
        } else if (answerMatch2) {
          answer = answerMatch2[1].toUpperCase();
        }
        
        // Extract explanation
        let explanation = '';
        
        // Try multiple patterns
        const explanationMatch1 = block.match(/explanation:?\s+(.+?)(?=\n\d+\.|$)/is);
        const explanationMatch2 = block.match(/(?:because|reason|as)\s+(.+?)(?=\n\d+\.|$)/is);
        
        if (explanationMatch1) {
          explanation = explanationMatch1[1].trim();
        } else if (explanationMatch2) {
          explanation = explanationMatch2[1].trim();
        }
        
        // Create the question object
        questions.push({
          question: questionText,
          options: filledOptions,
          answer: answer,
          explanation: explanation || "No explanation provided."
        });
        
      } catch (error) {
        logger.warn(`Error parsing MCQ block: ${error.message}`);
        continue;
      }
    }
  } else if (type === 'TF') {
    // Try multiple patterns to split text into questions for TF
    let questionBlocks = [];
    
    // Pattern 1: Split by numbered questions
    questionBlocks = text.split(/(?=^\s*\d+\s*[\.\)\:]\s+)/m).filter(Boolean);
    
    // Try other patterns if we didn't get enough
    if (questionBlocks.length < 3) {
      questionBlocks = text.split(/(?=Question\s*\d*\s*[:\.]\s+)/i).filter(Boolean);
    }
    
    if (questionBlocks.length < 3) {
      // Try to split by double newlines and look for patterns
      const chunks = text.split(/\n\s*\n/).filter(Boolean);
      questionBlocks = [];
      
      for (const chunk of chunks) {
        if (chunk.match(/^\s*\d+[\.\)\:]\s+/) || 
            chunk.match(/^Question/i) || 
            chunk.toLowerCase().includes("true") || 
            chunk.toLowerCase().includes("false")) {
          questionBlocks.push(chunk);
        }
      }
    }
    
    // Last resort: split by double newlines if we still don't have blocks
    if (questionBlocks.length < 2) {
      questionBlocks = text.split(/\n\s*\n/).filter(Boolean);
    }
    
    logger.debug(`Found ${questionBlocks.length} potential TF question blocks`);
    
    for (const block of questionBlocks) {
      try {
        // Skip if too short or likely not a question
        if (block.length < 20 || block.toLowerCase().includes("here are")) continue;
        
        // Extract question text - multiple patterns
        let questionText = '';
        
        // Try more patterns for question text
        const questionMatch = block.match(/^\s*(?:\d+[\.\)\:]\s+|Question\s*\d*\s*[:\.]\s+)(.+?)(?=\n|$)/m) || 
                             block.match(/^(.+?)(?=\n|$)/m);
        
        if (questionMatch) {
          questionText = questionMatch[1].trim();
        } else {
          // Just use the first line
          questionText = block.split('\n')[0].trim();
        }
        
        // Skip if looks like an introduction or too short
        if (questionText.length < 5 || 
            questionText.toLowerCase().includes("here are") || 
            questionText.toLowerCase().includes("i've created")) continue;
        
        // Extract the answer - default to true
        let answer = true;
        
        // Look for various ways the answer might be specified
        const falsePattern = /(?:answer|correct)(?:\s+is|:)\s+(false|f|no|wrong|incorrect|b)/i;
        const truePattern = /(?:answer|correct)(?:\s+is|:)\s+(true|t|yes|correct|a)/i;
        
        if (block.match(falsePattern)) {
          answer = false;
        } else if (block.match(truePattern)) {
          answer = true;
        }
        
        // Extract explanation
        let explanation = '';
        
        // Try multiple patterns
        const explanationMatch1 = block.match(/explanation:?\s+(.+?)(?=\n\d+\.|$)/is);
        const explanationMatch2 = block.match(/(?:because|reason|as)\s+(.+?)(?=\n\d+\.|$)/is);
        
        if (explanationMatch1) {
          explanation = explanationMatch1[1].trim();
        } else if (explanationMatch2) {
          explanation = explanationMatch2[1].trim();
        }
        
        // Create the question object
        questions.push({
          question: questionText,
          answer: answer,
          explanation: explanation || "No explanation provided."
        });
        
      } catch (error) {
        logger.warn(`Error parsing TF block: ${error.message}`);
        continue;
      }
    }
  }
  
  logger.info(`Successfully extracted ${questions.length} questions from text response`);
  return questions;
}

module.exports = {
  getNextApiKey,
  getCurrentKeyIndices,
  addApiKey,
  verifyApiKeys,
  testApiKey,
  generateQuestionsFromAPI,
  updateModelStats,
  selectModel,
  getModels
}; 