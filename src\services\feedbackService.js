// Feedback service for collecting and retrieving user feedback
const database = require('../database/database');
const logger = require('../utils/logger');
const fs = require('fs');
const path = require('path');

// Define path for the feedback JSON file
const FEEDBACK_FILE_PATH = path.join(__dirname, '../../data/feedback.json');

/**
 * Save feedback to a JSON file
 * @param {Object} feedbackData - The feedback data to save
 * @param {number} feedbackId - The ID of the feedback in the database
 */
async function saveFeedbackToJson(feedbackData, feedbackId) {
  try {
    // Create data directory if it doesn't exist
    const dataDir = path.join(__dirname, '../../data');
    try {
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true, mode: 0o755 });
        logger.info(`Created data directory at ${dataDir}`);
      }
    } catch (dirError) {
      logger.error(`<PERSON>rror creating data directory: ${dirError.message}`);
      // Continue with the function as the directory might already exist
    }
    
    // Prepare feedback object with ID and formatted date
    const feedbackWithId = {
      id: feedbackId,
      userId: feedbackData.userId,
      username: feedbackData.username || 'Unknown',
      rating: feedbackData.rating || 'opinion',
      suggestion: feedbackData.suggestion || null,
      quizType: feedbackData.quizType || 'Unknown',
      score: feedbackData.score || 0,
      timestamp: Date.now(),
      date: new Date().toISOString(),
      isRead: false
    };
    
    // Read existing feedback or create empty array
    let feedbackArray = [];
    if (fs.existsSync(FEEDBACK_FILE_PATH)) {
      try {
        const fileContent = fs.readFileSync(FEEDBACK_FILE_PATH, 'utf8');
        feedbackArray = JSON.parse(fileContent);
        
        // Make sure it's an array
        if (!Array.isArray(feedbackArray)) {
          logger.warn(`Feedback file exists but is not an array, creating new array`);
          feedbackArray = [];
        }
      } catch (parseError) {
        logger.error(`Error parsing feedback JSON file: ${parseError.message}`);
        logger.info(`Creating new feedback array as parsing failed`);
        feedbackArray = [];
      }
    } else {
      logger.info(`Feedback file does not exist yet, creating new file`);
    }
    
    // Add new feedback to the array
    feedbackArray.unshift(feedbackWithId); // Add to the beginning
    
    // Write the updated array back to the file
    try {
      fs.writeFileSync(FEEDBACK_FILE_PATH, JSON.stringify(feedbackArray, null, 2), 'utf8');
      logger.success(`Feedback saved to JSON file with ID: ${feedbackId}`);
    } catch (writeError) {
      logger.error(`Error writing feedback JSON file: ${writeError.message}`);
      // Try with a more direct approach as fallback
      const tempPath = path.join(dataDir, 'feedback.json.tmp');
      try {
        fs.writeFileSync(tempPath, JSON.stringify(feedbackArray, null, 2), 'utf8');
        fs.renameSync(tempPath, FEEDBACK_FILE_PATH);
        logger.success(`Feedback saved using temp file method with ID: ${feedbackId}`);
      } catch (fallbackError) {
        logger.error(`Fallback write also failed: ${fallbackError.message}`);
        return false;
      }
    }
    
    return true;
  } catch (error) {
    logger.error(`Error saving feedback to JSON file: ${error.message}`);
    logger.error(`Error stack: ${error.stack}`);
    return false;
  }
}

/**
 * Save user feedback to the database
 * @param {Object} feedbackData - Feedback data object
 * @param {string} feedbackData.userId - User's Telegram ID
 * @param {string} feedbackData.username - User's username
 * @param {string} feedbackData.rating - User's rating (good, neutral, bad)
 * @param {string} feedbackData.suggestion - User's suggestion text
 * @param {string} feedbackData.quizType - Type of quiz (MCQ, TF)
 * @param {number} feedbackData.score - User's score from the quiz
 * @returns {Promise<boolean>} - Success status
 */
async function saveFeedback(feedbackData) {
  return new Promise((resolve, reject) => {
    try {
      // Validate required fields and provide defaults
      if (!feedbackData) {
        logger.error('Missing feedback data object');
        resolve(false);
        return;
      }
      
      // Destructure with defaults
      const { 
        userId = null, 
        username = 'Unknown', 
        rating = 'opinion', 
        suggestion = null, 
        quizType = 'Unknown', 
        score = 0 
      } = feedbackData;
      
      // Check if userId is present
      if (!userId) {
        logger.error('Missing required userId in feedback data');
        resolve(false);
        return;
      }
      
      // Get a reference to the database
      const db = database.db();
      
      // Ensure database is initialized
      if (!db) {
        logger.error('Database not initialized in saveFeedback');
        resolve(false);
        return;
      }
      
      logger.info(`Saving feedback from user ${userId}, rating: ${rating}`);
      
      db.run(
        'INSERT INTO feedback (user_id, username, rating, suggestion, quiz_type, score, timestamp, is_read) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
        [userId, username, rating, suggestion, quizType, score, Date.now(), 0],
        async function(err) {
          if (err) {
            logger.error(`Error saving feedback to database: ${err.message}`);
            resolve(false);
            return;
          }
          
          const feedbackId = this.lastID;
          logger.success(`Feedback saved to database with ID: ${feedbackId}`);
          
          // Also save to JSON file
          try {
            const jsonSaved = await saveFeedbackToJson(feedbackData, feedbackId);
            if (!jsonSaved) {
              logger.warn(`Failed to save feedback to JSON file, but database save succeeded`);
            }
          } catch (jsonError) {
            logger.error(`Error saving to JSON file: ${jsonError.message}`);
            // Continue as database save was successful
          }
          
          resolve(true);
        }
      );
    } catch (error) {
      logger.error(`Error in saveFeedback: ${error.message}`);
      logger.error(`Error stack: ${error.stack}`);
      resolve(false);
    }
  });
}

/**
 * Update the JSON file when feedback is marked as read
 * @param {number} id - Feedback ID to update
 * @param {boolean} isRead - New read status
 */
async function updateFeedbackReadStatusInJson(id, isRead = true) {
  try {
    if (!fs.existsSync(FEEDBACK_FILE_PATH)) {
      return false;
    }
    
    // Read the feedback file
    const fileContent = fs.readFileSync(FEEDBACK_FILE_PATH, 'utf8');
    let feedbackArray = JSON.parse(fileContent);
    
    // Find and update the feedback entry
    const index = feedbackArray.findIndex(item => item.id === id);
    if (index !== -1) {
      feedbackArray[index].isRead = isRead;
      
      // Write the updated array back to the file
      fs.writeFileSync(FEEDBACK_FILE_PATH, JSON.stringify(feedbackArray, null, 2), 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    logger.error(`Error updating feedback read status in JSON: ${error.message}`);
    return false;
  }
}

/**
 * Mark feedback as read
 * @param {number} id - Feedback ID
 * @returns {Promise<boolean>} - Success status
 */
async function markFeedbackAsRead(id) {
  return new Promise((resolve, reject) => {
    try {
      // Get a reference to the database
      const db = database.db();
      
      if (!db) {
        logger.error('Database not initialized in markFeedbackAsRead');
        resolve(false);
        return;
      }
      
      db.run('UPDATE feedback SET is_read = 1 WHERE id = ?', [id], async function(err) {
        if (err) {
          logger.error('Error marking feedback as read:', err.message);
          resolve(false);
          return;
        }
        
        // Also update JSON file
        if (this.changes > 0) {
          await updateFeedbackReadStatusInJson(id, true);
        }
        
        resolve(this.changes > 0);
      });
    } catch (error) {
      logger.error('Error in markFeedbackAsRead:', error.message);
      resolve(false);
    }
  });
}

/**
 * Mark all feedback as read
 * @returns {Promise<boolean>} - Success status
 */
async function markAllFeedbackAsRead() {
  return new Promise((resolve, reject) => {
    try {
      // Get a reference to the database
      const db = database.db();
      
      if (!db) {
        logger.error('Database not initialized in markAllFeedbackAsRead');
        resolve(false);
        return;
      }
      
      db.run('UPDATE feedback SET is_read = 1 WHERE is_read = 0', async function(err) {
        if (err) {
          logger.error('Error marking all feedback as read:', err.message);
          resolve(false);
          return;
        }
        
        // If there are changes, update all unread items in JSON file
        if (this.changes > 0 && fs.existsSync(FEEDBACK_FILE_PATH)) {
          try {
            const fileContent = fs.readFileSync(FEEDBACK_FILE_PATH, 'utf8');
            let feedbackArray = JSON.parse(fileContent);
            
            // Update all unread feedback
            let updated = false;
            feedbackArray.forEach(item => {
              if (!item.isRead) {
                item.isRead = true;
                updated = true;
              }
            });
            
            if (updated) {
              fs.writeFileSync(FEEDBACK_FILE_PATH, JSON.stringify(feedbackArray, null, 2), 'utf8');
            }
          } catch (jsonError) {
            logger.error(`Error updating all feedback in JSON: ${jsonError.message}`);
          }
        }
        
        resolve(this.changes > 0);
      });
    } catch (error) {
      logger.error('Error in markAllFeedbackAsRead:', error.message);
      resolve(false);
    }
  });
}

/**
 * Get all feedback entries
 * @param {boolean} [unreadOnly=false] - Whether to return only unread feedback
 * @param {number} [limit=50] - Maximum number of records to return
 * @returns {Promise<Array>} - Array of feedback entries
 */
async function getAllFeedback(unreadOnly = false, limit = 50) {
  return new Promise((resolve, reject) => {
    try {
      // Get a reference to the database
      const db = database.db();
      
      if (!db) {
        logger.error('Database not initialized in getAllFeedback');
        resolve([]);
        return;
      }
      
      let query = `
        SELECT * FROM feedback
        ${unreadOnly ? 'WHERE is_read = 0' : ''}
        ORDER BY timestamp DESC
        LIMIT ?
      `;
      
      db.all(query, [limit], (err, rows) => {
        if (err) {
          logger.error('Error getting feedback:', err.message);
          resolve([]);
          return;
        }
        
        resolve(rows || []);
      });
    } catch (error) {
      logger.error('Error in getAllFeedback:', error.message);
      resolve([]);
    }
  });
}

/**
 * Get feedback statistics
 * @returns {Promise<Object>} - Feedback statistics
 */
async function getFeedbackStats() {
  return new Promise((resolve, reject) => {
    try {
      // Get a reference to the database
      const db = database.db();
      
      if (!db) {
        logger.error('Database not initialized in getFeedbackStats');
        resolve({
          total: 0,
          unread: 0,
          ratings: { good: 0, neutral: 0, bad: 0 }
        });
        return;
      }
      
      const stats = {
        total: 0,
        unread: 0,
        ratings: {
          good: 0,
          neutral: 0,
          bad: 0
        }
      };
      
      // Get total count
      db.get('SELECT COUNT(*) as count FROM feedback', [], (err, row) => {
        if (err) {
          logger.error('Error getting feedback count:', err.message);
          resolve(stats);
          return;
        }
        
        stats.total = row.count;
        
        // Get unread count
        db.get('SELECT COUNT(*) as count FROM feedback WHERE is_read = 0', [], (err, row) => {
          if (err) {
            logger.error('Error getting unread feedback count:', err.message);
            resolve(stats);
            return;
          }
          
          stats.unread = row.count;
          
          // Get rating counts
          db.all('SELECT rating, COUNT(*) as count FROM feedback GROUP BY rating', [], (err, rows) => {
            if (err) {
              logger.error('Error getting feedback ratings:', err.message);
              resolve(stats);
              return;
            }
            
            rows.forEach(row => {
              if (row.rating in stats.ratings) {
                stats.ratings[row.rating] = row.count;
              }
            });
            
            resolve(stats);
          });
        });
      });
    } catch (error) {
      logger.error('Error in getFeedbackStats:', error.message);
      resolve({
        total: 0,
        unread: 0,
        ratings: { good: 0, neutral: 0, bad: 0 }
      });
    }
  });
}

/**
 * Get feedback from JSON file
 * @param {boolean} [unreadOnly=false] - Whether to return only unread feedback
 * @param {number} [limit=100] - Maximum number of entries to return
 * @returns {Array} - Array of feedback entries from JSON file
 */
function getFeedbackFromJson(unreadOnly = false, limit = 100) {
  try {
    if (!fs.existsSync(FEEDBACK_FILE_PATH)) {
      return [];
    }
    
    // Read the feedback file
    const fileContent = fs.readFileSync(FEEDBACK_FILE_PATH, 'utf8');
    let feedbackArray = JSON.parse(fileContent);
    
    // Filter by read status if needed
    if (unreadOnly) {
      feedbackArray = feedbackArray.filter(item => !item.isRead);
    }
    
    // Limit the number of entries
    return feedbackArray.slice(0, limit);
  } catch (error) {
    logger.error(`Error reading feedback from JSON file: ${error.message}`);
    return [];
  }
}

/**
 * Save feedback directly to JSON file (backup method when database fails)
 * @param {Object} feedbackData - The feedback data to save
 * @returns {Promise<boolean>} Success status
 */
async function saveFeedbackDirectToJson(feedbackData) {
  try {
    // Create data directory if it doesn't exist
    const dataDir = path.join(__dirname, '../../data');
    if (!fs.existsSync(dataDir)) {
      try {
        fs.mkdirSync(dataDir, { recursive: true, mode: 0o755 });
      } catch (err) {
        logger.error(`Failed to create data directory: ${err.message}`);
        return false;
      }
    }
    
    // Create a unique ID for the feedback
    const uniqueId = Date.now() + '-' + Math.floor(Math.random() * 10000);
    
    // Prepare feedback object with defaults for missing fields
    const feedbackObject = {
      id: uniqueId,
      userId: feedbackData.userId || 'unknown',
      username: feedbackData.username || 'Unknown',
      rating: feedbackData.rating || 'opinion',
      suggestion: feedbackData.suggestion || null,
      quizType: feedbackData.quizType || 'Unknown',
      score: feedbackData.score || 0,
      timestamp: Date.now(),
      date: new Date().toISOString(),
      isRead: false,
      savedDirectly: true // Flag to indicate this was saved directly to JSON
    };
    
    // Read existing feedback or create empty array
    let feedbackArray = [];
    if (fs.existsSync(FEEDBACK_FILE_PATH)) {
      try {
        const fileContent = fs.readFileSync(FEEDBACK_FILE_PATH, 'utf8');
        feedbackArray = JSON.parse(fileContent);
        
        if (!Array.isArray(feedbackArray)) {
          feedbackArray = [];
        }
      } catch (err) {
        logger.error(`Error reading feedback file: ${err.message}`);
        feedbackArray = [];
      }
    }
    
    // Add the new feedback to the beginning of the array
    feedbackArray.unshift(feedbackObject);
    
    // Write the updated array back to the file
    try {
      fs.writeFileSync(FEEDBACK_FILE_PATH, JSON.stringify(feedbackArray, null, 2), 'utf8');
      logger.success(`Feedback saved directly to JSON with ID: ${uniqueId}`);
      return true;
    } catch (err) {
      logger.error(`Error writing feedback file: ${err.message}`);
      
      // Try with temp file approach
      try {
        const tempPath = path.join(dataDir, 'feedback.json.tmp');
        fs.writeFileSync(tempPath, JSON.stringify(feedbackArray, null, 2), 'utf8');
        fs.renameSync(tempPath, FEEDBACK_FILE_PATH);
        logger.success(`Feedback saved to JSON using temp file method`);
        return true;
      } catch (tempErr) {
        logger.error(`Failed to save with temp file too: ${tempErr.message}`);
        return false;
      }
    }
  } catch (error) {
    logger.error(`Error in saveFeedbackDirectToJson: ${error.message}`);
    return false;
  }
}

module.exports = {
  saveFeedback,
  getAllFeedback,
  getFeedbackStats,
  markFeedbackAsRead,
  markAllFeedbackAsRead,
  getFeedbackFromJson,
  saveFeedbackDirectToJson
}; 