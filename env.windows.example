#============================================================
# MCQ_TF Bot - Windows Environment Configuration
#============================================================

#------------------------------------------------------------
# Telegram Bot Settings
#------------------------------------------------------------
# Your Telegram bot token from BotFather
TELEGRAM_BOT_TOKEN=123456789:ABCDEFGHIJKLMNOPQRSTUVWXYZ

# Comma-separated list of admin user IDs (no spaces)
ADMIN_IDS=123456789,987654321

# (Optional) Required channel ID that users must join to use the bot
# Example: -1001234567890 or @channel_username
REQUIRED_CHANNEL_ID=

#------------------------------------------------------------
# API Configuration
#------------------------------------------------------------
# OpenRouter API Key - Required for AI model access
API_KEY=sk-or-v1-...

# AI Models to use (OpenRouter model IDs, comma-separated)
MODELS=google/gemini-2.0-flash-exp:free,nvidia/llama-3.1-nemotron-ultra-253b-v1:free,google/gemma-3-27b-it:free

# API timeout in milliseconds (3 minutes default)
API_TIMEOUT=180000

#------------------------------------------------------------
# Performance Settings
#------------------------------------------------------------
# Concurrency settings
MAX_CONCURRENT_REQUESTS=12
MAX_REQUESTS_PER_MODEL=3
MAX_REQUESTS_PER_USER=4

# Cache settings
CACHE_TTL=86400
MAX_CACHE_SIZE=500

# Rate limiting
REQUESTS_PER_DAY=20
FILE_UPLOADS_PER_DAY=10

#------------------------------------------------------------
# Document Processing Settings
#------------------------------------------------------------
# Questions per page for documents
QUESTIONS_PER_PAGE=5

# Number of questions for image uploads
IMAGE_QUESTIONS_COUNT=10

# Path to Python executable (adjust based on your installation)
PYTHON_PATH=python

# Path to Tesseract OCR executable on Windows
TESSERACT_PATH=C:\\Program Files\\Tesseract-OCR\\tesseract.exe

# Performance settings for extraction
EXTRACTION_PARALLEL_WORKERS=6
EXTRACTION_TIMEOUT_MS=1200000
EXTRACTION_CACHE_SIZE=200
EXTRACTION_CACHE_TTL=14400000

# Directories for temporary files and logs
TEMP_DIR=./temp
LOGS_DIR=./logs

#------------------------------------------------------------
# Debug Settings
#------------------------------------------------------------
# Enable debug mode (true/false)
DEBUG_MODE=false

# Log level (error, warn, info, debug)
LOG_LEVEL=info

# Detailed model logging (true/false)
LOG_MODEL_STATS=true 