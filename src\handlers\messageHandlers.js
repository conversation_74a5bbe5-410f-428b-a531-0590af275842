// Import required modules
const path = require('path');
const fs = require('fs');
const { execSync } = require('child_process');
const sharp = require('sharp');
const axios = require('axios');
const pdf = require('pdf-parse');
const pdfjsLib = require('pdfjs-dist/legacy/build/pdf.js');
const os = require('os');
const { performance } = require('perf_hooks');
const { downloadFile, extractTextFromDocument } = require('../services/fileService_fixed');
const apiService = require('../services/apiService');
const { getSession, saveSession, updateSession, resetSession } = require('../services/sessionService');
const questionUtils = require('../utils/questionUtils');
const database = require('../database/database');
const { escapeHtml } = require('../utils/helpers');
const config = require('../config');
const logger = require('../utils/logger');
const { generateFileName } = require('../utils/fileUtils');
const userService = require('../services/userService');
const { isAdmin } = require('../utils/userUtils');
const { checkFileUploadLimit } = require('../database/database');

// Set the worker source for pdfjsLib
pdfjsLib.GlobalWorkerOptions.workerSrc = path.join(process.cwd(), 'node_modules', 'pdfjs-dist/legacy/build/pdf.worker.js');

/**
 * Process a user's text message to generate questions
 * @param {object} ctx - Telegram context
 */
async function handleTextMessage(ctx) {
  try {
    await ctx.reply('عذراً، لا يمكنني معالجة الرسائل النصية. يرجى إرسال صورة أو مستند فقط.', {
      reply_markup: {
        inline_keyboard: [
          [{ text: 'العودة إلى القائمة الرئيسية', callback_data: 'main_menu' }]
        ]
      }
    });
  } catch (error) {
    console.error('Error handling text message:', error);
    await ctx.reply('حدث خطأ أثناء معالجة النص. يرجى المحاولة مرة أخرى.');
  }
}

/**
 * Process a user's photo message to generate questions
 * @param {object} ctx - Telegram context
 */
async function handlePhotoMessage(ctx) {
  try {
    // Get user ID
    const userId = ctx.from.id;
    const userName = ctx.from.username || `${ctx.from.first_name || ''} ${ctx.from.last_name || ''}`.trim();
    
    // Check if session exists
    let userSession = getSession(userId) || { type: 'MCQ' };
    
    // Create empty session if not exists
    if (!userSession) {
      userSession = { type: 'MCQ' };
    }
    
    // Check if user is an admin
    const userIsAdmin = await isAdmin(userId);
    
    // Skip file upload limit check for admins
    if (!userIsAdmin) {
      const uploadLimit = await checkFileUploadLimit(userId);
      
      if (!uploadLimit.allowed) {
        const resetDate = new Date(uploadLimit.resetAt);
        const hours = Math.ceil(uploadLimit.remainingTime);
        let timeMessage = '';
        
        if (hours < 1) {
          timeMessage = 'أقل من ساعة';
        } else if (hours === 1) {
          timeMessage = 'ساعة واحدة';
        } else if (hours < 24) {
          timeMessage = `${hours} ساعات`;
        } else {
          timeMessage = `${Math.floor(hours / 24)} يوم و ${hours % 24} ساعات`;
        }
        
        await ctx.reply(`عذراً، لقد وصلت إلى الحد الأقصى للملفات المسموح بها يومياً. يمكنك المحاولة مرة أخرى بعد ${timeMessage}.`);
        return;
      }
    } else {
      // Log that admin is bypassing the limit
      logger.info(`Admin user ${userId} bypassing file upload limit`);
    }
    
    // Create processing message
    const processingMsg = await ctx.reply('جاري معالجة الصورة واستخراج النص... ⏳');
    
    // Log the photo processing
    logger.user(`Received photo from ${ctx.from.id}`);
    
    // Track files to delete in case of error
    const filesToDelete = [];
    
    try {
      // Get the highest quality photo
      const photos = ctx.message.photo;
      const photo = photos[photos.length - 1];
      
      // Get the file ID and download it
      const fileId = photo.file_id;
      
      // Download photo as a file path
      const fs = require('fs');  // Explicitly import fs here
      const filePath = await downloadFile(ctx.telegram, fileId);
      
      // Add to files to delete
      if (filePath) {
        filesToDelete.push(filePath);
      }
      
      // Extract text from photo using OCR
      const result = await extractTextFromDocument(filePath);
      
      if (!result || !result.text) {
        // No text extracted, clean up and notify user
        setTimeout(() => deleteUserFiles(filesToDelete), 1000);
        await ctx.reply('لم يتم استخراج نص كافٍ من الصورة. يرجى إرسال صورة بنص أوضح أو أكثر.');
        return;
      }
      
      const extractedText = result.text;
      
      // Add extracted text file to cleanup list
      if (result.extractedTextPath) {
        filesToDelete.push(result.extractedTextPath);
      }
      
      // Check if text was extracted successfully
      if (!extractedText || extractedText.length < 10) {
        // Cleanup files
        setTimeout(() => deleteUserFiles(filesToDelete), 1000);
        await ctx.reply('لم يتم التعرف على نص كافٍ في الصورة. يرجى إرسال صورة أوضح أو ملف PDF/نصي بدلاً من ذلك.');
        return;
      }
      
      // Update processing message
      await ctx.telegram.editMessageText(
        ctx.chat.id,
        processingMsg.message_id,
        null,
        'تم استخراج النص من الصورة، جاري إنشاء الأسئلة... ⏳'
      );
      
      // Set up the session data
      const config = require('../config');
      userSession.text = extractedText;
      userSession.isOCR = true;
      // Use the specific image questions count instead of general questionsPerPage
      userSession.count = config.imageQuestionsCount;
      
      // Save file paths for cleanup
      userSession.filePath = filePath;
      if (result.extractedTextPath) {
        userSession.extractedTextPath = result.extractedTextPath;
      }
      
      // Save the session
      await saveSession(userId, userSession);
      
      // Generate questions
      await processContentAndGenerateQuestions(ctx, extractedText, userSession, processingMsg.message_id);
    } catch (extractionError) {
      // Clean up any files that were created
      if (filesToDelete.length > 0) {
        setTimeout(() => deleteUserFiles(filesToDelete), 1000);
      }
      
      logger.error(`Image extraction error: ${extractionError.message}`);
      await ctx.telegram.editMessageText(
        ctx.chat.id,
        processingMsg.message_id,
        null,
        'فشل في استخراج النص من الصورة. تأكد من أن بايثون وتيسيراكت مثبتان بشكل صحيح. 🙁'
      );
      await ctx.reply('يرجى التأكد من تثبيت بايثون وتيسيراكت حسب التعليمات في الملف PYTHON_EXTRACTION_README.md');
    }
  } catch (error) {
    logger.error(`Error handling photo message: ${error.message}`);
    await ctx.reply('حدث خطأ أثناء معالجة الصورة. يرجى المحاولة مرة أخرى أو إرسال المحتوى كملف PDF أو نص.');
  }
}

/**
 * Process a user's document message to generate questions
 * @param {object} ctx - Telegram context
 */
async function handleDocumentMessage(ctx) {
  try {
    // Get user ID
    const userId = ctx.from.id;
    const userName = ctx.from.username || `${ctx.from.first_name || ''} ${ctx.from.last_name || ''}`.trim();
    
    // Check if session exists
    let userSession = getSession(userId) || { type: 'MCQ' };
    
    // Create empty session if not exists
    if (!userSession) {
      userSession = { type: 'MCQ' };
    }
    
    // Check if user is an admin
    const userIsAdmin = await isAdmin(userId);
    
    // Skip file upload limit check for admins
    if (!userIsAdmin) {
      const uploadLimit = await checkFileUploadLimit(userId);
      
      if (!uploadLimit.allowed) {
        const resetDate = new Date(uploadLimit.resetAt);
        const hours = Math.ceil(uploadLimit.remainingTime);
        let timeMessage = '';
        
        if (hours < 1) {
          timeMessage = 'أقل من ساعة';
        } else if (hours === 1) {
          timeMessage = 'ساعة واحدة';
        } else if (hours < 24) {
          timeMessage = `${hours} ساعات`;
        } else {
          timeMessage = `${Math.floor(hours / 24)} يوم و ${hours % 24} ساعات`;
        }
        
        await ctx.reply(`عذراً، لقد وصلت إلى الحد الأقصى للملفات المسموح بها يومياً. يمكنك المحاولة مرة أخرى بعد ${timeMessage}.`);
        return;
      }
    } else {
      // Log that admin is bypassing the limit
      logger.info(`Admin user ${userId} bypassing file upload limit`);
    }
    
    const document = ctx.message.document;
    
    // Check file type
    const config = require('../config');
    const allowedTypes = config.supportedFileTypes;
    if (!allowedTypes.includes(document.mime_type)) {
      await ctx.reply('عذراً، نوع الملف غير مدعوم. يرجى التأكد من إرسال ملف بتنسيق مدعوم مثل PDF أو Word أو PowerPoint أو Excel أو ملف نصي أو صورة.');
      return;
    }
    
    // File size check removed - users can now upload files of any size
    
    // Notify user about processing - updated message for large files
    const processingMsg = await ctx.reply('جاري معالجة المستند واستخراج النص... قد تستغرق المستندات الكبيرة وقتًا أطول للمعالجة. ⏳');
    
    // Track files to clean up
    const filesToDelete = [];
    
    try {
      // Reset the session to ensure fresh processing
      // Explicitly clear any existing questions in the session
      if (userSession.questions) {
        delete userSession.questions;
        delete userSession.generatedAt;
        await saveSession(userId, userSession);
        logger.info(`Reset previous questions for user ${userId} to ensure fresh generation`);
      }
      
      // Log the document processing
      logger.user(`Received document from ${ctx.from.id}`);
      
      try {
        // Download the document to a file on disk
        const filePath = await downloadFile(ctx.telegram, document.file_id);
        
        // Add to files to delete
        if (filePath) {
          filesToDelete.push(filePath);
        }
        
        // Extract text from the document file
        logger.info(`Attempting to extract text from ${filePath}`);
        const result = await extractTextFromDocument(filePath);
        
        if (!result) {
          // Cleanup files
          setTimeout(() => deleteUserFiles(filesToDelete), 1000);
          logger.error(`Failed to extract text: Result is empty`);
          await ctx.reply('Failed to extract text from the document. Please try again or use a different file.');
          return;
        }
        
        const extractedText = result.text;
        
        // Add extracted text file to cleanup
        if (result.extractedTextPath) {
          filesToDelete.push(result.extractedTextPath);
        }
        
        // Make sure we get the page count from the result, with fallback to 1
        const pageCount = result.pageCount || 1;
        logger.info(`Document has ${pageCount} pages`);
        const config = require('../config');
        // Calculate questions based on page count and questions per page setting
        const questionCount = Math.max(1, pageCount * config.questionsPerPage);
        logger.info(`Generating ${questionCount} questions (${config.questionsPerPage} per page)`);
        
        // Detect if this is a scanned document from extraction output
        const isScanned = (typeof result === 'object' && result.isScanned) || 
                          (typeof extractedText === 'string' && 
                           (extractedText.includes('OCR') || 
                            extractedText.includes('scanned') || 
                            extractedText.includes('Converting pages to images')));
        
        if (isScanned) {
          logger.info(`Document detected as scanned document (using OCR)`);
          userSession.isScanned = true;
        }
        
        // Validate extracted text
        if (!extractedText || extractedText.length < 100) {
          // Cleanup files
          setTimeout(() => deleteUserFiles(filesToDelete), 1000);
          await ctx.reply('لم يتم استخراج نص كافٍ من المستند. يرجى إرسال مستند بنص أكثر.');
          return;
        }
        
        // Log a sample of what was extracted for debugging
        const textSample = extractedText.substring(0, 150).replace(/\n/g, ' ');
        logger.debug(`Document text sample: "${textSample}..."`);
        
        // Update user message with file info
        let statusMessage = 'تم استخراج النص من المستند، جاري إنشاء الأسئلة... ⏳';
        if (pageCount > 1) {
          statusMessage = `تم استخراج النص من المستند (${pageCount} صفحات)، سيتم إنشاء ${questionCount} سؤال (${config.questionsPerPage} أسئلة لكل صفحة)... ⏳`;
        } else {
          statusMessage = `تم استخراج النص من المستند، سيتم إنشاء ${questionCount} سؤال... ⏳`;
        }
        
        await ctx.telegram.editMessageText(
          ctx.chat.id,
          processingMsg.message_id,
          null,
          statusMessage
        );
        
        // Set the question count in the session
        userSession.count = questionCount;
        
        // Store file paths for later cleanup
        userSession.filePath = filePath;
        if (result.extractedTextPath) {
          userSession.extractedTextPath = result.extractedTextPath;
        }
        // Store all extracted text paths if available
        if (result.extractedTextPaths && Array.isArray(result.extractedTextPaths)) {
          userSession.extractedTextPaths = result.extractedTextPaths;
        }
        
        // Update session with the text
        userSession.text = extractedText;
        
        // Force disable cache lookup for this user to get fresh questions
        userSession.disableCache = true;
        
        await saveSession(userId, userSession);
        
        // Generate questions - passing fresh session
        await processContentAndGenerateQuestions(ctx, extractedText, userSession, processingMsg.message_id);
        
      } catch (extractionError) {
        // Clean up any created files
        if (filesToDelete.length > 0) {
          setTimeout(() => deleteUserFiles(filesToDelete), 1000);
        }
        
        logger.error(`Document extraction error: ${extractionError.message}`);
        await ctx.telegram.editMessageText(
          ctx.chat.id,
          processingMsg.message_id,
          null,
          ' تعذر استخراج النص من المستند. 🙁'
        );
        await ctx.reply('يرجى المحاولة مرة أخرى');
      }
    } catch (error) {
      // Clean up any created files
      if (filesToDelete.length > 0) {
        setTimeout(() => deleteUserFiles(filesToDelete), 1000);
      }
      
      logger.error(`Error handling document message: ${error.message}`);
      await ctx.reply('حدث خطأ أثناء معالجة المستند. يرجى المحاولة مرة أخرى.');
    }
  } catch (error) {
    logger.error(`Error handling document message: ${error.message}`);
    await ctx.reply('حدث خطأ أثناء معالجة المستند. يرجى المحاولة مرة أخرى.');
  }
}

/**
 * Check if we have cached questions for this content
 * @param {string} text - The original text content
 * @param {string} type - Question type (MCQ or TF)
 * @returns {Promise<Array|null>} - Array of questions or null if not found
 */
async function findSimilarCache(text, type, userId) {
  try {
    // Check user session for cache disable flag
    if (userId) {
      const userSession = await getSession(userId);
      if (userSession && userSession.disableCache === true) {
        logger.debug(`Cache lookup disabled for user ${userId}`);
        return null;
      }
    }
    
    // Safely get database instance
    const db = database.db();
    
    if (!db) {
      logger.error('Database not initialized in findSimilarCache');
      return null;
    }
    
    // Create a cache key/hash for this content
    const contentHash = questionUtils.generateHash(text, type);
    const expectedType = questionUtils.getFullTypeName(type).toLowerCase();
    
    logger.debug(`Looking for cache match with key: ${contentHash}`);
    
    // Try to get data directly from database using wrapped promise
    return new Promise((resolve) => {
      // First try an exact match with the hash
      db.get('SELECT data FROM cache WHERE id = ?', 
        [contentHash], 
        async (err, row) => {
          if (err) {
            logger.error(`Error querying cache: ${err.message}`);
            resolve(null);
            return;
          }
          
          // If we found an exact match, use it
          if (row && row.data) {
            try {
              logger.success(`Cache hit for key: ${contentHash}`);
              const questions = JSON.parse(row.data);
              resolve(questions);
              return;
            } catch (parseError) {
              logger.error(`Error parsing cached questions: ${parseError.message}`);
              resolve(null);
              return;
            }
          }
          
          // If no match was found, return null
          resolve(null);
        });
    });
  } catch (error) {
    logger.error(`Error in findSimilarCache: ${error.message}`);
    return null;
  }
}

/**
 * Find the best match based on text similarity
 * @param {Array} rows - Database rows to check
 * @param {string} inputText - Filtered input text to compare against
 * @returns {Promise<Array|null>} - Array of questions or null
 */
async function findBestSimilarityMatch(rows, inputText) {
  // The best match and its similarity score
  let bestMatch = null;
  let bestScore = 0;
  
  for (const row of rows) {
    try {
      // Skip entries with no text for comparison
      if (!row.filtered_text && !row.content_text) {
        continue;
      }
      
      let rowFilteredText = row.filtered_text;
      
      // If we don't have filtered text, generate it from content_text
      if (!rowFilteredText && row.content_text) {
        rowFilteredText = questionUtils.filterTextForCaching(row.content_text);
      }
      
      // Skip if we still don't have any filtered text
      if (!rowFilteredText) continue;
      
      // Calculate similarity score
      const similarityScore = questionUtils.calculateSimilarity(
        inputText,
        rowFilteredText
      );
      
      console.log(`Cache entry ${row.id}: similarity ${similarityScore.toFixed(4)}`);
      
      if (similarityScore > bestScore) {
        try {
          // Parse the questions data
          const cachedQuestions = JSON.parse(row.data);
          
          if (!cachedQuestions || cachedQuestions.length === 0) {
            continue; // Skip if no valid questions
          }
          
          bestScore = similarityScore;
          bestMatch = cachedQuestions;
          
          // If we found a good match (≥60% similarity), stop searching
          if (similarityScore >= 0.6) {
            console.log(`Found excellent similarity match (${similarityScore.toFixed(4)})`);
            break;
          }
        } catch (parseError) {
          console.error(`Error parsing cached questions: ${parseError.message}`);
        }
      }
    } catch (error) {
      console.error(`Error processing row in similarity check: ${error.message}`);
    }
  }
  
  // Return best match if it has a sufficient similarity score
  if (bestScore >= 0.5) {
    return bestMatch;
  }
  
  return null;
}

/**
 * Update processing message with progress indicator
 * @param {object} ctx - Telegram context
 * @param {number} messageId - Message ID to update
 * @param {number} percent - Progress percentage (0-100)
 * @param {string} [state] - Current state description
 * @returns {Promise<void>}
 */
async function updateProcessingProgress(ctx, messageId, percent, state = '') {
  try {
    // Ensure percent is between 0 and 100
    percent = Math.max(0, Math.min(100, percent));
    
    // Select an emoji based on the percentage
    let statusEmoji = '';
    if (percent < 20) statusEmoji = '🔍';
    else if (percent < 40) statusEmoji = '📝';
    else if (percent < 60) statusEmoji = '⚙️';
    else if (percent < 80) statusEmoji = '📊';
    else if (percent < 100) statusEmoji = '🔄';
    else statusEmoji = '✅';
    
    // Create a modern progress bar with 15 segments from red to green
    const totalSegments = 15;
    const filledSegments = Math.floor((percent / 100) * totalSegments);
    
    // Color progression from red to orange to yellow to green
    const progressSymbols = ['🟥', '🟧', '🟨', '🟩'];
    let progressBar = '';
    
    for (let i = 0; i < totalSegments; i++) {
      if (i < filledSegments) {
        // Calculate which color to use based on position in the bar
        // We want red at the beginning, then orange, yellow, and green at the end
        const position = i / (totalSegments - 1);
        let colorIndex;
        
        if (position < 0.33) {
          colorIndex = 0; // Red for first third
        } else if (position < 0.66) {
          colorIndex = 1; // Orange for middle third
        } else if (position < 0.9) {
          colorIndex = 2; // Yellow for last portion
        } else {
          colorIndex = 3; // Green at the very end
        }
        
        progressBar += progressSymbols[colorIndex];
      } else {
        progressBar += '⬜';
      }
    }
    
    // Add pulsing effect for active state
    let loadingIndicator = '';
    const animationPhase = Math.floor(Date.now() / 300) % 3;
    if (percent < 100) {
      if (animationPhase === 0) loadingIndicator = '⏳ ⚡ ⚡';
      else if (animationPhase === 1) loadingIndicator = '⚡ ⏳ ⚡';
      else loadingIndicator = '⚡ ⚡ ⏳';
    } else {
      loadingIndicator = '✨ ✨ ✨';
    }
    
    // Format the percentage with leading zeros
    const formattedPercent = percent.toString().padStart(3, ' ');
    
    // Create progress message with percentage and optional state
    const title = `<b>جاري توليد الأسئلة ${loadingIndicator}</b>`;
    const progressSection = `\n\n${progressBar}\n${statusEmoji} <b>${formattedPercent}%</b>`;
    const stateText = state ? `\n<i>${state}</i>` : '';
    
    const progressMessage = `${title}${progressSection}${stateText}`;
    
    // Update the message
    await ctx.telegram.editMessageText(
      ctx.chat.id,
      messageId,
      null,
      progressMessage,
      { parse_mode: 'HTML' }
    );
  } catch (error) {
    // Silently catch errors - we don't want to interrupt the main process for UI updates
    console.log(`Error updating progress: ${error.message}`);
  }
}

/**
 * Clean up extracted text by removing page markers and other artifacts
 * @param {string} text - Extracted text from document
 * @returns {string} - Cleaned text
 */
function cleanExtractedText(text) {
  if (!text) return '';
  
  // Remove page markers like "--- Page 1 ---"
  let cleaned = text.replace(/\n?---\s*Page\s+\d+\s*---\n?/g, ' ');
  
  // Remove additional page count references
  cleaned = cleaned.replace(/Page count: \d+/g, '');
  cleaned = cleaned.replace(/\[Note: This is a summary from selected pages of a large document\]/g, '');
  cleaned = cleaned.replace(/document has \d+ pages/ig, 'document');
  cleaned = cleaned.replace(/this document contains \d+ pages?/ig, 'this document');
  cleaned = cleaned.replace(/this is a \d+-page document/ig, 'this document');
  cleaned = cleaned.replace(/this document is \d+ pages? long/ig, 'this document');
  cleaned = cleaned.replace(/total of \d+ pages?/ig, '');
  
  // Remove statements about page numbers
  cleaned = cleaned.replace(/page \d+ of \d+/ig, '');
  cleaned = cleaned.replace(/\(page \d+\)/ig, '');
  cleaned = cleaned.replace(/\[page \d+\]/ig, '');
  
  // Remove lines that just talk about page counts or document structure
  cleaned = cleaned.replace(/^.*?\b\d+ pages?\b.*?$/gm, '');
  cleaned = cleaned.replace(/^.*?\bpage count\b.*?$/gm, '');
  cleaned = cleaned.replace(/^.*?\bdocument structure\b.*?$/gm, '');
  cleaned = cleaned.replace(/^.*?\bsingle page\b.*?$/gm, '');
  cleaned = cleaned.replace(/^.*?\bone page\b.*?$/gm, '');
  cleaned = cleaned.replace(/^.*?\bmultiple pages?\b.*?$/gm, '');
  
  // Remove metadata about document format
  cleaned = cleaned.replace(/(?:^|\n)File type:.*?(?:\n|$)/g, '\n');
  cleaned = cleaned.replace(/(?:^|\n)Format:.*?(?:\n|$)/g, '\n');
  cleaned = cleaned.replace(/(?:^|\n)Pages:.*?(?:\n|$)/g, '\n');
  
  // Remove footer/header markers that mention page numbers
  cleaned = cleaned.replace(/\bpage \d+\b/ig, '');
  
  // Normalize whitespace
  cleaned = cleaned.replace(/\s{2,}/g, ' ').trim();
  
  return cleaned;
}

/**
 * Process the content and generate questions based on session type
 * @param {object} ctx - Telegram context
 * @param {string} text - The text content to generate questions from
 * @param {object} session - User session data
 * @param {number} messageId - ID of the processing message to update
 */
async function processContentAndGenerateQuestions(ctx, text, session, messageId) {
  // Track when this process started for timeout detection
  const processStartTime = Date.now();
  
  try {
    const userId = ctx.from.id;
    const chatId = ctx.chat.id;
    
    // Keep track of files to delete later
    const filesToDelete = [];
    if (session.filePath) {
      filesToDelete.push(session.filePath);
    }
    if (session.extractedTextPath) {
      filesToDelete.push(session.extractedTextPath);
    }
    // Add all paths from extractedTextPaths array if it exists
    if (session.extractedTextPaths && Array.isArray(session.extractedTextPaths)) {
      filesToDelete.push(...session.extractedTextPaths);
    }
    
    // Keep user informed - send "still working" messages every 30 seconds
    const updateInterval = 30000; // 30 seconds
    const updateIntervalId = setInterval(async () => {
      const elapsedSeconds = Math.floor((Date.now() - processStartTime) / 1000);
      try {
        await ctx.telegram.editMessageText(
          chatId,
          messageId,
          null,
          `لا زلنا نعمل على تحليل النص واستخراج الأسئلة... (${elapsedSeconds} ثانية) ⏳`
        );
      } catch (error) {
        // Ignore errors updating the message
        logger.debug(`Could not update progress message: ${error.message}`);
      }
    }, updateInterval);
    
    // Reset any previously cached questions to ensure fresh generation
    if (session.questions) {
      delete session.questions;
      delete session.generatedAt;
      logger.info(`Reset previous questions for user ${userId} to ensure fresh generation`);
    }
    
    // Force-update the session to persist the reset
    await saveSession(userId, session);
    
    // Show initial progress
    await updateProcessingProgress(ctx, messageId, 10, '🔎 تحليل المحتوى وتحديد نوع الأسئلة...');
    
    // Use the question count from the session
    const questionCount = session.count || 15;
    
    // Clean the text to remove page markers before processing
    const cleanedText = cleanExtractedText(text);
    
    // Check if we have a text sample for debugging
    if (!session.disableCache) {
      logger.debug(`Looking for cached questions matching this content...`);
      
      // Check if we have cached questions we can use - pass userId for cache flag check
      const cachedQuestions = await findSimilarCache(cleanedText, session.type, userId);
      
      if (cachedQuestions && cachedQuestions.length > 0) {
        logger.info(`Found ${cachedQuestions.length} cached questions for user ${userId}`);
        
        // Update progress
        await updateProcessingProgress(ctx, messageId, 95, '✅ تم إيجاد أسئلة مناسبة!');
        
        // Clear the update interval
        clearInterval(updateIntervalId);
        
        // Update the processing message
        await ctx.telegram.editMessageText(
          chatId,
          messageId,
          null,
          'تم إيجاد أسئلة مناسبة! ✅'
        );
        
        // Save the questions in the session
        await saveSession(userId, {
          ...session,
          questions: cachedQuestions,
          generatedAt: Date.now()
        });
        
        // Clean up files
        if (filesToDelete.length > 0) {
          setTimeout(() => deleteUserFiles(filesToDelete), 1000);
        }
        
        // Ask user if they want to take a test or view answers
        await askQuestionPreference(ctx, cachedQuestions, session);
        return;
      }
    } else {
      logger.info(`Cache lookup disabled for user ${userId}, generating fresh questions`);
    }
    
    // Log a sample of the cleaned text for debugging
    const textSample = cleanedText.length > 200 ? cleanedText.substring(0, 200) + '...' : cleanedText;
    logger.debug(`Cleaned text sample: ${textSample}`);
    
    logger.debug(`Processing text for questions (${cleanedText.length} chars), generating ${questionCount} questions for user ${userId}`);
    
    // Update progress - preparing to generate questions
    await updateProcessingProgress(ctx, messageId, 40, '🤖 جاري تحليل النص وإنشاء أسئلة جديدة...');
    
    // Update progress - generating questions
    await updateProcessingProgress(ctx, messageId, 60, '📝 جاري صياغة الأسئلة والخيارات...');
    
    // Detect if this is likely a scanned document based on OCR artifacts
    const isScanned = cleanedText.includes('OCR') || 
                     session.isScanned || 
                     cleanedText.includes('Tesseract') ||
                     (cleanedText.match(/[^\x00-\x7F]/g)?.length > cleanedText.length * 0.3); // High non-ASCII character ratio
    
    if (isScanned) {
      logger.info(`Using optimized processing for scanned document (user ${userId})`);
    }
    
    // Track API request start time
    const apiStartTime = Date.now();
    let questions = [];
    
    try {
      // Generate questions through API service
      logger.info(`[${userId}] Generating ${questionCount} ${session.type} questions using model`);
      
      // Use cleaned text for generating questions
      questions = await apiService.generateQuestionsFromAPI(cleanedText, session.type, questionCount, 2, isScanned, userId);
      
      // Calculate API response time
      const apiResponseTime = (Date.now() - apiStartTime) / 1000;
      logger.info(`Got ${questions.length} questions from API in ${apiResponseTime.toFixed(1)} seconds`);
      
      // Update progress - formatting questions
      await updateProcessingProgress(ctx, messageId, 90, '✅ تم إنشاء الأسئلة، جاري التنسيق...');
    } catch (apiError) {
      // Handle API errors - generate dummy questions
      logger.error(`API error for user ${userId}: ${apiError.message}`);
      questions = generateDefaultQuestions(cleanedText, session.type, questionCount);
      await ctx.telegram.editMessageText(
        ctx.chat.id,
        messageId,
        null,
        'فشل في إنشاء الأسئلة. يرجى المحاولة مرة أخرى. 🙁'
      );
    }
    
    // Clean up explanations to avoid JSON fragments or formatting issues
    questions = questions.map(q => ({
      ...q,
      explanation: questionUtils.cleanExplanationText(q.explanation)
    }));
    
    // Post-process questions to remove any reference to page counts
    questions = questions.map(q => {
      // Remove page count references from questions
      const cleanQuestion = q.question
        .replace(/\bpage \d+\b/ig, '')
        .replace(/\b\d+ pages?\b/ig, '')
        .replace(/\bsingle page\b/ig, '')
        .replace(/\bone page\b/ig, '')
        .replace(/\bmultiple pages?\b/ig, '');
      
      // Similarly clean explanation
      const cleanExplanation = q.explanation
        .replace(/\bpage \d+\b/ig, '')
        .replace(/\b\d+ pages?\b/ig, '')
        .replace(/\bsingle page\b/ig, '')
        .replace(/\bone page\b/ig, '')
        .replace(/\bmultiple pages?\b/ig, '');
      
      return {
        ...q,
        question: cleanQuestion,
        explanation: cleanExplanation
      };
    });
    
    // Update progress - formatting results
    await updateProcessingProgress(ctx, messageId, 80, '📊 جاري تدقيق الأسئلة وتنظيمها...');
    
    // Ensure we never have more questions than requested
    if (questions && questions.length > questionCount) {
      questions = questions.slice(0, questionCount);
    }
    
    // Update progress - finalizing
    await updateProcessingProgress(ctx, messageId, 90, '✨ جاري تجهيز الأسئلة للعرض...');
    
    // Update progress - complete
    await updateProcessingProgress(ctx, messageId, 100, '✅ تم إنشاء الأسئلة بنجاح!');
    
    // Update the processing message and send results
    await ctx.telegram.editMessageText(
      chatId,
      messageId,
      null,
      'تم إنشاء الأسئلة بنجاح! ✅'
    );
    
    // Save the questions in the session
    await saveSession(userId, {
      ...session,
      questions: questions,
      generatedAt: Date.now()
    });
    
    // Stop the regular updates
    clearInterval(updateIntervalId);
    
    // Clean up user's files now that we've generated the questions
    if (filesToDelete.length > 0) {
      logger.info(`Cleaning up ${filesToDelete.length} files for user ${userId}`);
      // Run file deletion asynchronously so it doesn't block the response
      setTimeout(() => {
        deleteUserFiles(filesToDelete);
      }, 1000);
    }
    
    // Ask user if they want to take a test or view answers
    await askQuestionPreference(ctx, questions, session);
    
  } catch (error) {
    logger.error(`Error processing content: ${error.message}`);
    await ctx.reply('حدث خطأ أثناء إنشاء الأسئلة. يرجى المحاولة مرة أخرى.');
  }
}

/**
 * Delete files uploaded by the user after processing
 * @param {Array<string>} filePaths - Paths of files to delete
 */
function deleteUserFiles(filePaths = []) {
  if (!filePaths || filePaths.length === 0) {
    logger.info('No files to delete - file paths array is empty');
    return;
  }

  const fs = require('fs');
  const path = require('path');
  
  // Remove duplicates from file paths array
  const uniqueFilePaths = [...new Set(filePaths)];
  
  logger.info(`Starting deletion of ${uniqueFilePaths.length} files`);
  
  // Project directories to check for related files
  const projectDir = path.resolve(__dirname, '../..');
  const uploadsDir = path.join(projectDir, 'uploads');
  const extractedDir = path.join(projectDir, 'extracted');
  
  // For each file path
  uniqueFilePaths.forEach(filePath => {
    if (!filePath) {
      return;
    }
    
    try {
      // Get the file name to look for related files
      const fileName = path.basename(filePath);
      
      // Look for the pattern in different locations
      const relatedPatterns = [
        // Original file
        filePath,
        // Original plus _extracted.txt
        `${filePath}_extracted.txt`,
        // In uploads dir with same name
        path.join(uploadsDir, fileName),
        // In uploads with _extracted suffix
        path.join(uploadsDir, `${fileName}_extracted.txt`),
        // In extracted dir with same name
        path.join(extractedDir, fileName),
        // In extracted with _extracted suffix
        path.join(extractedDir, `${fileName}_extracted.txt`)
      ];
      
      // Delete all related files that exist
      let deletedCount = 0;
      let deletedFiles = [];
      
      relatedPatterns.forEach(pattern => {
        try {
          if (fs.existsSync(pattern)) {
            fs.unlinkSync(pattern);
            deletedCount++;
            deletedFiles.push(path.basename(pattern));
          }
        } catch (error) {
          // Silent fail - no need to log every failure
        }
      });
      
      // Log a single message with the result
      if (deletedCount > 0) {
        logger.info(`✅ Successfully deleted ${deletedCount} files for: ${path.basename(filePath)}`);
      }
      
    } catch (error) {
      logger.error(`❌ Error during file cleanup: ${error.message}`);
    }
  });
  
  logger.info('File cleanup completed');
}

/**
 * Ask the user what they want to do with the generated questions
 * @param {object} ctx - Telegram context
 * @param {Array} questions - Array of question objects
 * @param {object} session - User session data
 */
async function askQuestionPreference(ctx, questions, session) {
  try {
    // Create a keyboard with options
    const keyboard = {
      inline_keyboard: [
        [
          { text: 'بدء الاختبار 🧠', callback_data: 'take_interactive_test' },
          { text: 'عرض الأسئلة والإجابات 📝', callback_data: 'show_answers' }
        ]
      ]
    };
    
    // Send message with keyboard - without mentioning the exact count
    await ctx.reply('تم إنشاء الأسئلة بنجاح. ماذا تريد أن تفعل الآن؟', { reply_markup: keyboard });
    
  } catch (error) {
    logger.error(`Error asking question preference: ${error.message}`);
    // Fall back to showing questions with answers
    await sendQuestions(ctx, questions, session);
  }
}

/**
 * Send questions without answers (for test taking)
 * @param {object} ctx - Telegram context
 * @param {Array} questions - Array of question objects
 * @param {object} session - User session data
 */
async function sendQuestionsWithoutAnswers(ctx, questions, session) {
  try {
    // Prepare header
    let message = session.type === 'MCQ' ? 
      '<b>📝 اسئلة خيارات متعددة (MCQ)  </b>\n\n' : 
      '<b>✅ اختبار صح/خطأ</b>\n\n';
    
    // Format questions without answers
    for (let i = 0; i < questions.length; i++) {
      const question = questions[i];
      message += `<b>${i + 1}. ${escapeHtml(question.question)}</b>\n`;
      
      if (session.type === 'MCQ') {
        // Add options
        for (let j = 0; j < question.options.length; j++) {
          const letter = String.fromCharCode(65 + j);
          // Remove any existing letter prefixes from the options
          let cleanedOption = question.options[j].replace(/^[A-D][\)\.]?\s*/, '');
          message += `   ${letter}) ${escapeHtml(cleanedOption)}\n`;
        }
      }
      
      message += '\n';
      
      // Send in chunks if message gets too long
      if (message.length > 3500) {
        await ctx.reply(message, { parse_mode: 'HTML' });
        message = '';
      }
    }
    
    // Send any remaining content
    if (message.length > 0) {
      await ctx.reply(message, { parse_mode: 'HTML' });
    }
    
    // Provide options
    await ctx.reply('اختر ما تريد فعله الآن:', {
      reply_markup: {
        inline_keyboard: [
          [
            { text: 'بدء الاختبار التفاعلي 📝', callback_data: 'take_interactive_test' }
          ],
          [
            { text: 'عرض الإجابات الصحيحة ✅', callback_data: 'show_answers' },
            { text: 'العودة للقائمة الرئيسية 🏠', callback_data: 'main_menu' }
          ]
        ]
      }
    });
    
  } catch (error) {
    console.error('Error sending questions without answers:', error);
    await ctx.reply('حدث خطأ أثناء إرسال الأسئلة. يرجى المحاولة مرة أخرى.');
  }
}

/**
 * Clean explanation text removing any JSON fragments or other artifacts
 * @param {string} explanation - Raw explanation text
 * @returns {string} Cleaned explanation text
 */
function cleanExplanationText(explanation) {
  if (!explanation) return '';
  
  // Convert to string if not already
  const text = String(explanation);
  
  // Remove any JSON fragments that might be included
  if (text.includes('"question":') || 
      text.includes('"answer":') ||
      text.includes('"explanation":')) {
    
    // Extract just the first sentence or a reasonable chunk
    const firstSentence = text.split(/[.!?][\s\n]/)[0];
    if (firstSentence && firstSentence.length > 10) {
      return firstSentence + '.';
    }
    
    // If we can't get a good sentence, just return a generic explanation
    return 'See provided explanation.';
  }
  
  // Check for brackets which might indicate JSON fragments
  if (text.includes('{') && text.includes('}')) {
    // Try to get text before the bracket
    const beforeBracket = text.split('{')[0].trim();
    if (beforeBracket.length > 10) {
      return beforeBracket + '.';
    }
  }
  
  // Remove quotes and other JSON artifacts
  const cleaned = text
    .replace(/\\"/g, '"')
    .replace(/^"/, '')
    .replace(/"$/, '')
    .replace(/\\n/g, ' ')
    .replace(/\\t/g, ' ')
    .replace(/\s{2,}/g, ' ')
    .trim();
  
  return cleaned;
}

/**
 * Send questions with their answers to the user
 * @param {object} ctx - Telegram context
 * @param {Array} questions - Array of question objects
 * @param {object} session - User session data
 */
async function sendQuestions(ctx, questions, session) {
  try {
    // Prepare header based on question type
    let header = '';
    if (session.type === 'MCQ') {
      header = '<b>📝 اسئلة خيارات متعددة (MCQ)</b>\n\n';
    } else if (session.type === 'TF') {
      header = '<b>✅ أسئلة صح وخطأ</b>\n\n';
    }
    
    // Prepare messages in chunks to avoid hitting Telegram message length limits
    let currentMessage = header;
    let messages = [];
    
    for (let i = 0; i < questions.length; i++) {
      const question = questions[i];
      let questionText = `<b>${i + 1}. ${escapeHtml(question.question)}</b>\n`;
      
      if (session.type === 'MCQ') {
        let correctOptionLetter = '';
        
        // Determine the correct answer letter
        if ('correctOption' in question) {
          correctOptionLetter = question.correctOption;
        } else if ('answer' in question) {
          if (typeof question.answer === 'string' && ['A', 'B', 'C', 'D'].includes(question.answer)) {
            correctOptionLetter = question.answer;
          } else if (typeof question.answer === 'string' && question.options) {
            const answerIndex = question.options.findIndex(opt => opt === question.answer);
            if (answerIndex !== -1) {
              correctOptionLetter = String.fromCharCode(65 + answerIndex);
            }
          }
        }
        
        // Add options with markings for the correct one
        for (let j = 0; j < question.options.length; j++) {
          const letter = String.fromCharCode(65 + j);
          const marked = letter === correctOptionLetter ? '✓ ' : '';
          // Remove any existing letter prefixes from the options, making sure they are actually prefixes
          let cleanedOption = question.options[j].replace(/^([A-D][\)\.\:]|\([A-D]\))\s+/, '');
          questionText += `   ${letter}) ${marked}${escapeHtml(cleanedOption)}\n`;
        }
        
        // Add correct answer
        if (correctOptionLetter) {
          questionText += `<b>الإجابة الصحيحة: ${correctOptionLetter}</b>\n`;
        } else {
          questionText += `<b>الإجابة الصحيحة: غير متوفرة</b>\n`;
        }
      } else if (session.type === 'TF') {
        // Determine if the question is true based on either isTrue or answer property
        const isQuestionTrue = 'isTrue' in question ? question.isTrue : 
                            typeof question.answer === 'boolean' ? question.answer : 
                            question.answer?.toLowerCase() === 'true';
        // Add correct answer 
        const correctAnswer = isQuestionTrue ? 'صح ✓' : 'خطأ ✗';
        questionText += `<b>الإجابة الصحيحة: ${correctAnswer}</b>\n`;
      }
      
      // Add explanation if available
      if (question.explanation) {
        const cleanedExplanation = questionUtils.cleanExplanationText(question.explanation);
        questionText += `<i>التفسير: ${escapeHtml(cleanedExplanation)}</i>\n`;
      }
      
      // Add separator
      questionText += '\n';
      
      // Check if adding this question would exceed Telegram's message limit
      if ((currentMessage + questionText).length > 4000) {
        // Push current message and start a new one
        messages.push(currentMessage);
        currentMessage = questionText;
      } else {
        // Add to current message
        currentMessage += questionText;
      }
    }
    
    // Add any remaining content
    if (currentMessage.length > 0) {
      messages.push(currentMessage);
    }
    
    // Send messages
    for (const message of messages) {
      await ctx.reply(message, { parse_mode: 'HTML' });
    }
    
  } catch (error) {
    console.error('Error sending questions:', error);
    await ctx.reply('حدث خطأ أثناء إرسال الأسئلة. يرجى المحاولة مرة أخرى.');
  }
}

/**
 * Start an interactive test showing one question at a time with answer buttons
 * @param {object} ctx - Telegram context
 * @param {Array} questions - Array of question objects
 * @param {object} session - User session data
 * @param {number} questionIndex - Current question index (0-based)
 */
async function startInteractiveTest(ctx, questions, session, questionIndex = 0) {
  try {
    // Check if we've reached the end of the questions - handle this case first for efficiency
    if (questionIndex >= questions.length) {
      return await showQuizSummary(ctx, session);
    }
    
    // Clean up any existing feedback message from the session
    cleanupPreviousMessages(ctx, session).catch(error => {
      console.log(`Error in cleanup: ${error.message}`);
    });
    
    // Prepare the current question
    const question = questions[questionIndex];
    if (!question) {
      console.error(`Question at index ${questionIndex} is undefined!`);
      await ctx.reply('حدث خطأ في السؤال. يرجى إعادة بدء الاختبار.');
      return;
    }
    
    // Format and send the question
    return await sendQuestionMessage(ctx, question, questions.length, questionIndex, session);
    
  } catch (error) {
    console.error('Error in interactive test:', error);
    await ctx.reply('حدث خطأ أثناء تقديم السؤال. يرجى المحاولة مرة أخرى.');
  }
}

/**
 * Clean up previous messages in the session
 * @param {object} ctx - Telegram context
 * @param {object} session - User session
 */
async function cleanupPreviousMessages(ctx, session) {
  if (!session) return;
  
  // Delete previous feedback message if exists
  if (session.feedbackMessageId) {
    try {
      await ctx.telegram.deleteMessage(ctx.chat.id, session.feedbackMessageId);
    } catch (error) {
      logger.debug(`Could not delete feedback message: ${error.message}`);
    }
  }
  
  // Delete previous question message if exists
  if (session.activeQuestionMessageId) {
    try {
      await ctx.telegram.deleteMessage(ctx.chat.id, session.activeQuestionMessageId);
    } catch (error) {
      logger.debug(`Could not delete question message: ${error.message}`);
    }
  }
}

/**
 * Show a summary of the quiz results to the user
 * @param {object} ctx - Telegram context
 * @param {object} session - User session data
 */
async function showQuizSummary(ctx, session) {
  try {
    if (!session || !session.quizScore) {
      await ctx.reply('عذراً، لا توجد نتائج للاختبار. يرجى إجراء اختبار أولاً.');
      return;
    }
    
    // Calculate score and percentage
    const correct = session.quizScore.correct;
    const total = session.quizScore.total || session.questions?.length || 0;
    const percentage = Math.round((correct / total) * 100);
    
    // Prepare feedback based on performance
    let feedback = '';
      if (percentage >= 90) {
      feedback = 'ممتاز! أداء رائع! 🏆';
      } else if (percentage >= 75) {
      feedback = 'جيد جداً! استمر في التقدم! 🌟';
      } else if (percentage >= 60) {
      feedback = 'جيد! يمكنك تحسين أدائك! 👍';
      } else if (percentage >= 40) {
      feedback = 'متوسط. حاول مراجعة الموضوع مرة أخرى. 📚';
      } else {
      feedback = 'تحتاج إلى مزيد من الدراسة. لا تستسلم! 📚';
    }
    
    // Check user's remaining file uploads per day
    const database = require('../database/database');
    const userId = ctx.from.id;
    const config = require('../config');
    let fileUploadsInfo = '';
    
    try {
      // Get database instance to check current upload count directly
      const db = database.db();
      const maxUploads = config.fileUploadsPerDay || 5;
      
      if (db) {
        // Use a promise to handle the asynchronous database query
        const getFileUploadsCount = () => {
          return new Promise((resolve) => {
            db.get('SELECT count FROM file_uploads WHERE user_id = ?', [userId], (err, row) => {
              if (err) {
                console.error('Error getting file upload count:', err);
                resolve('');
              } else {
                if (row) {
                  const usedUploads = row.count || 0;
                  const remaining = Math.max(0, maxUploads - usedUploads);
                  resolve(`\n\n<b>📄 عدد الملفات المتبقية اليوم:</b> ${remaining} من ${maxUploads}`);
                } else {
                  resolve(`\n\n<b>📄 عدد الملفات المتبقية اليوم:</b> ${maxUploads} من ${maxUploads}`);
                }
              }
            });
          });
        };
        
        // Wait for the database query to complete
        fileUploadsInfo = await getFileUploadsCount();
      }
    } catch (limitError) {
      console.error('Error getting file upload limits:', limitError);
      // Continue without adding upload info if there's an error
    }
    
    // Prepare summary message
    const summaryMsg = 
      `<b>🎯 نتيجة الاختبار</b>\n\n` +
      `<b>عدد الإجابات الصحيحة:</b> ${correct} من ${total}\n` +
      `<b>النسبة المئوية:</b> ${percentage}%\n\n` +
      `<b>التقييم:</b> ${feedback}` +
      fileUploadsInfo;
    
    // Send summary with action buttons
    await ctx.reply(summaryMsg, {
            parse_mode: 'HTML',
            reply_markup: {
              inline_keyboard: [
                [
            { text: 'عرض تقرير مفصل 📋', callback_data: 'detailed_report' },
            { text: 'شاركنا رأيك 💬', callback_data: 'request_feedback' }
          ],
          [
            { text: 'اختبار جديد 🔄', callback_data: 'main_menu' }
          ]
        ]
      }
    });
    
    // Record quiz completion in stats
    const statsService = require('../services/statsService');
    statsService.recordQuizCompletion({
      userId: ctx.from.id,
      username: ctx.from.username || ctx.from.first_name || 'Unknown',
      quizType: session.type,
      topic: session.topic || 'Unknown',
      score: correct,
      totalQuestions: total
    });
    
  } catch (error) {
    console.error('Error showing quiz summary:', error);
    await ctx.reply('حدث خطأ أثناء عرض ملخص الاختبار. يرجى المحاولة مرة أخرى.');
  }
}

/**
 * Show a detailed report of the user's quiz performance
 * @param {object} ctx - Telegram context
 */
async function showDetailedReport(ctx) {
  try {
    const userId = ctx.from.id;
    const session = await getSession(userId);
    
    if (!session || !session.questions || !session.quizScore || !session.quizScore.answers) {
      await ctx.reply('لا توجد معلومات كافية لعرض تقرير مفصل. يرجى إجراء اختبار أولاً.');
      return;
    }
    
    // Check if we're already showing a report to prevent duplicates
    if (session.showingDetailedReport) {
      console.log(`Already showing detailed report for user ${userId}, ignoring duplicate request`);
      return;
    }
    
    // Mark that we're showing a report
    await updateSession(userId, { showingDetailedReport: true });
    
    try {
      // Create a detailed report
      const reports = [];
      let reportText = '<b>📊 تقرير مفصل للاختبار 📊</b>\n\n';
      
      // Summary statistics
      const correct = session.quizScore.correct;
      const total = session.quizScore.total;
      const percentage = Math.round((correct / total) * 100);
      
      reportText += `<b>النتيجة الإجمالية:</b> ${correct}/${total} (${percentage}%)\n\n`;
      reportText += '<b>تفاصيل الإجابات:</b>\n\n';
      
      // Maximum size for each report part to avoid Telegram message size limits
      const MAX_REPORT_LENGTH = 3500;
      
      // Detail of each answer
      for (let i = 0; i < session.quizScore.answers.length; i++) {
        const answer = session.quizScore.answers[i];
        const questionIndex = answer.questionIndex;
        const question = session.questions[questionIndex];
        const questionNum = questionIndex + 1;
        
        let questionReport = `<b>سؤال ${questionNum}:</b> ${escapeHtml(question.question)}\n`;
        
        if (session.type === 'MCQ') {
          let userAnswerLetter = answer.userAnswer;
          let userAnswerText = '';
          let correctOptionLetter = '';
          let correctOptionText = '';
          
          // Get user answer text
          if (question.options && userAnswerLetter) {
            const userAnswerIndex = userAnswerLetter.charCodeAt(0) - 65;
            if (userAnswerIndex >= 0 && userAnswerIndex < question.options.length) {
              userAnswerText = question.options[userAnswerIndex];
            }
          }
          
          // Determine the correct answer
          if ('correctOption' in question) {
            correctOptionLetter = question.correctOption;
            if (question.options) {
              const correctIndex = correctOptionLetter.charCodeAt(0) - 65;
              if (correctIndex >= 0 && correctIndex < question.options.length) {
                correctOptionText = question.options[correctIndex];
              }
            }
          } else if ('answer' in question) {
            if (typeof question.answer === 'string' && ['A', 'B', 'C', 'D'].includes(question.answer)) {
              correctOptionLetter = question.answer;
              if (question.options) {
                const correctIndex = correctOptionLetter.charCodeAt(0) - 65;
                if (correctIndex >= 0 && correctIndex < question.options.length) {
                  correctOptionText = question.options[correctIndex];
                }
              }
            } else if (typeof question.answer === 'string' && question.options) {
              const answerIndex = question.options.findIndex(opt => opt === question.answer);
              if (answerIndex !== -1) {
                correctOptionLetter = String.fromCharCode(65 + answerIndex);
                correctOptionText = question.options[answerIndex];
              }
            }
          }
          
          questionReport += `<b>إجابتك:</b> ${userAnswerLetter}) ${escapeHtml(userAnswerText)}\n`;
          
          if (!answer.isCorrect) {
            questionReport += `<b>الإجابة الصحيحة:</b> ${correctOptionLetter}) ${escapeHtml(correctOptionText)}\n`;
          }
      } else {
          const userAnswerText = answer.userAnswer === 'true' ? 'صح' : 'خطأ';
          // Determine if the question is true based on either isTrue or answer property
          const isQuestionTrue = 'isTrue' in question ? question.isTrue : 
                               typeof question.answer === 'boolean' ? question.answer : 
                               question.answer?.toLowerCase() === 'true';
          const correctAnswerText = isQuestionTrue ? 'صح' : 'خطأ';
          
          questionReport += `<b>إجابتك:</b> ${userAnswerText}\n`;
          
          if (!answer.isCorrect) {
            questionReport += `<b>الإجابة الصحيحة:</b> ${correctAnswerText}\n`;
          }
        }
        
        // Add result indicator
        questionReport += answer.isCorrect ? '<b>النتيجة:</b> ✅ صحيح\n' : '<b>النتيجة:</b> ❌ خطأ\n';
        
        // Add explanation if available
        if (question.explanation) {
          questionReport += `<b>التفسير:</b> ${escapeHtml(cleanExplanationText(question.explanation))}\n\n`;
        }
        
        questionReport += '\n';
        
        // Check if adding this question would exceed message limit
        if ((reportText + questionReport).length > MAX_REPORT_LENGTH) {
          reports.push(reportText);
          reportText = questionReport;
        } else {
          reportText += questionReport;
        }
      }
      
      // Add any remaining content
      if (reportText.length > 0) {
        reports.push(reportText);
      }
      
      // Send all report parts
      await sendReportChunks(reports);
    } finally {
      // Reset the flag when done, with a slight delay to prevent fast clicking
      setTimeout(async () => {
        await updateSession(userId, { showingDetailedReport: false });
      }, 2000);
    }
    
    /**
     * Send report in chunks to avoid message size limitations
     * @param {Array<string>} reports - Array of report text chunks
     */
    async function sendReportChunks(reports) {
      for (let i = 0; i < reports.length; i++) {
        const isLastChunk = i === reports.length - 1;
        
        if (isLastChunk) {
          // Add action buttons to the last chunk
          await ctx.reply(reports[i], {
          parse_mode: 'HTML',
          reply_markup: {
            inline_keyboard: [
              [
                  { text: 'عرض الإجابات فقط 📝', callback_data: 'show_answers' },
                  { text: 'العودة للقائمة الرئيسية 🏠', callback_data: 'main_menu' }
                ]
              ]
            }
          });
        } else {
          await ctx.reply(reports[i], { parse_mode: 'HTML' });
        }
      }
    }
    
  } catch (error) {
    console.error('Error showing detailed report:', error);
    await ctx.reply('حدث خطأ أثناء عرض التقرير المفصل. يرجى المحاولة مرة أخرى.');
    
    // Reset the flag in case of error
    try {
      await updateSession(ctx.from.id, { showingDetailedReport: false });
    } catch (e) {
      console.error('Error resetting showingDetailedReport flag:', e);
    }
  }
}

/**
 * Request feedback from the user about their quiz experience
 * @param {object} ctx - Telegram context
 */
async function requestFeedback(ctx) {
  try {
    const userId = ctx.from.id;
    console.log(`Requesting feedback from user ${userId}`);
    
    // Set user session to feedback mode
    await updateSession(userId, { state: 'awaiting_feedback' });
    
    // Send feedback request message with all options in one menu
    const feedbackMsg = await ctx.reply('شاركنا رأيك حول أداء البوت وتجربة الاختبار:', {
      reply_markup: {
        inline_keyboard: [
          [
            { text: '👍 جيد', callback_data: 'feedback_good_direct' },
            { text: '👎 سيء', callback_data: 'feedback_bad_direct' }
          ],
          [
            { text: '✍️ كتابة رأي', callback_data: 'feedback_opinion' }
          ],
          [
            { text: 'إلغاء ❌', callback_data: 'cancel_feedback' }
              ]
            ]
          }
        });
    
    // Store the message ID for later deletion
    await updateSession(userId, { feedbackMessageId: feedbackMsg.message_id });
    
    // Answer callback query if this is invoked from a callback
    if (ctx.callbackQuery) {
      await ctx.answerCbQuery();
    }
    
  } catch (error) {
    console.error('Error requesting feedback:', error);
    if (ctx.callbackQuery) {
      await ctx.answerCbQuery('حدث خطأ أثناء طلب التقييم');
    }
    await ctx.reply('حدث خطأ أثناء طلب التقييم. يرجى المحاولة مرة أخرى لاحقاً.');
  }
}

/**
 * Handle user's feedback rating (good/bad)
 * @param {object} ctx - Telegram context
 * @param {string} rating - User's rating (good/bad/opinion)
 */
async function handleFeedbackRating(ctx, rating) {
  try {
    const userId = ctx.from.id;
    const session = await getSession(userId);
    
    if (!session) {
      await ctx.answerCbQuery('حدث خطأ في الجلسة. يرجى المحاولة مرة أخرى.');
      return;
    }
    
    // Store the rating in the session if not opinion
    if (rating !== 'opinion') {
      await updateSession(userId, { 
        feedbackRating: rating,
        state: 'awaiting_feedback_suggestion'
      });
    } else {
      // For opinion option, just set state
      await updateSession(userId, { 
        state: 'awaiting_feedback_suggestion'
      });
    }
    
    // Delete the rating message
    if (session.feedbackMessageId) {
      try {
        await ctx.telegram.deleteMessage(ctx.chat.id, session.feedbackMessageId);
      } catch (error) {
        console.log(`Could not delete feedback message: ${error.message}`);
      }
    }
    
    // Request suggestions based on selection
    if (rating === 'opinion') {
      // For opinion, directly ask for text feedback
      const msg = await ctx.reply('يرجى كتابة رأيك أو اقتراحك لتحسين البوت:', {
        reply_markup: {
          inline_keyboard: [
            [
              { text: 'إلغاء ❌', callback_data: 'cancel_feedback' }
            ]
          ]
        }
      });
      
      // Store message ID
      await updateSession(ctx.from.id, { feedbackMessageId: msg.message_id });
      
      // Answer callback query
      await ctx.answerCbQuery('يرجى كتابة رأيك');
    } else {
      // For good/bad, ask if they want to add suggestions
      await requestSuggestion(ctx, rating);
      
      // Answer callback query
      await ctx.answerCbQuery('شكراً على تقييمك!');
    }
    
  } catch (error) {
    console.error('Error handling feedback rating:', error);
    await ctx.answerCbQuery('حدث خطأ أثناء معالجة التقييم');
    await ctx.reply('حدث خطأ أثناء معالجة التقييم. يرجى المحاولة مرة أخرى.');
  }
}

/**
 * Request suggestions from the user after rating
 * @param {object} ctx - Telegram context
 * @param {string} rating - User's rating (good/bad)
 */
async function requestSuggestion(ctx, rating) {
  try {
    // Prepare message based on rating
    let message = '';
    if (rating === 'good') {
      message = 'رائع! نحن سعداء أنك استمتعت بالاختبار. هل لديك أي اقتراحات للتحسين؟';
    } else {
      message = 'نأسف لتجربتك السيئة. هل يمكنك مشاركة ما الذي لم يعجبك أو كيف يمكننا التحسين؟';
    }
    
    // Send suggestion request
    const suggestionMsg = await ctx.reply(message, {
      reply_markup: {
        inline_keyboard: [
          [
            { text: 'لا، شكراً ✓', callback_data: 'no_suggestion' }
          ],
          [
            { text: 'إلغاء ❌', callback_data: 'cancel_feedback' }
          ]
        ]
      }
    });
    
    // Store message ID
    await updateSession(ctx.from.id, { feedbackMessageId: suggestionMsg.message_id });
    
  } catch (error) {
    console.error('Error requesting suggestion:', error);
    await ctx.reply('حدث خطأ أثناء طلب الاقتراحات. شكراً على تقييمك!');
  }
}

/**
 * Handle user's text suggestion
 * @param {object} ctx - Telegram context
 */
async function handleSuggestionText(ctx) {
  try {
    const userId = ctx.from.id;
    const session = await getSession(userId);
    
    if (!session || session.state !== 'awaiting_feedback_suggestion') {
      console.log(`Invalid session state for suggestion: ${session?.state}`);
      return;
    }
    
    const suggestionText = ctx.message.text;
    
    // Prepare feedback data
    const feedbackData = {
      userId: userId,
      username: ctx.from.username || ctx.from.first_name || 'Unknown',
      rating: session.feedbackRating || 'opinion', // Default to 'opinion' if not set
      suggestion: suggestionText,
      quizType: session.type || 'Unknown',
      score: session.quizScore ? 
        Math.round((session.quizScore.correct / session.quizScore.total) * 100) : 0
    };
    
    // Get the feedback service
    const feedbackService = require('../services/feedbackService');
    
    // Try to save to database first
    try {
      const saved = await feedbackService.saveFeedback(feedbackData);
      
      if (!saved) {
        // If database save failed, try direct JSON save
        console.log(`Database feedback save failed, trying direct JSON save`);
        await feedbackService.saveFeedbackDirectToJson(feedbackData);
      }
    } catch (saveError) {
      console.error(`Error saving feedback to database: ${saveError.message}`);
      
      // Try direct JSON save as fallback
      try {
        await feedbackService.saveFeedbackDirectToJson(feedbackData);
      } catch (jsonError) {
        console.error(`Also failed to save feedback to JSON: ${jsonError.message}`);
      }
    }
    
    // Delete the suggestion request message if it exists
    if (session.feedbackMessageId) {
      try {
        await ctx.telegram.deleteMessage(ctx.chat.id, session.feedbackMessageId);
      } catch (error) {
        console.log(`Could not delete feedback message: ${error.message}`);
      }
    }
    
    // Reset session state
    await updateSession(userId, { 
      state: null,
      feedbackMessageId: null,
      feedbackRating: null
    });
    
    // Thank the user
    await ctx.reply('شكراً على ملاحظاتك! سنأخذها بعين الاعتبار لتحسين البوت. 🙏');
    
  } catch (error) {
    console.error('Error handling suggestion text:', error);
    await ctx.reply('حدث خطأ أثناء حفظ اقتراحك. شكراً على وقتك!');
  }
}

/**
 * Handle when user doesn't want to provide a suggestion
 * @param {object} ctx - Telegram context
 */
async function handleNoSuggestion(ctx) {
  try {
    const userId = ctx.from.id;
    const session = await getSession(userId);
    
    if (!session) {
      await ctx.answerCbQuery('حدث خطأ في الجلسة.');
      return;
    }
    
    // Prepare feedback data
    const feedbackData = {
      userId: userId,
      username: ctx.from.username || ctx.from.first_name || 'Unknown',
      rating: session.feedbackRating,
      suggestion: null,
      quizType: session.type || 'Unknown',
      score: session.quizScore ? 
        Math.round((session.quizScore.correct / session.quizScore.total) * 100) : 0
    };
    
    // Get the feedback service
    const feedbackService = require('../services/feedbackService');
    
    // Try to save to database first
    try {
      const saved = await feedbackService.saveFeedback(feedbackData);
      
      if (!saved) {
        // If database save failed, try direct JSON save
        console.log(`Database feedback save failed, trying direct JSON save`);
        await feedbackService.saveFeedbackDirectToJson(feedbackData);
      }
    } catch (saveError) {
      console.error(`Error saving feedback to database: ${saveError.message}`);
      
      // Try direct JSON save as fallback
      try {
        await feedbackService.saveFeedbackDirectToJson(feedbackData);
      } catch (jsonError) {
        console.error(`Also failed to save feedback to JSON: ${jsonError.message}`);
      }
    }
    
    // Delete the message
    if (session.feedbackMessageId) {
      try {
        await ctx.telegram.deleteMessage(ctx.chat.id, session.feedbackMessageId);
      } catch (error) {
        console.log(`Could not delete feedback message: ${error.message}`);
      }
    }
    
    // Reset session state
    await updateSession(userId, { 
      state: null,
      feedbackMessageId: null,
      feedbackRating: null
    });
    
    // Thank the user
    await ctx.reply('شكراً على تقييمك! 🙏');
    await ctx.answerCbQuery('تم إرسال تقييمك بنجاح.');
    
  } catch (error) {
    console.error('Error handling no suggestion:', error);
    await ctx.answerCbQuery('حدث خطأ أثناء معالجة ردك');
    await ctx.reply('حدث خطأ أثناء معالجة ردك. شكراً على وقتك!');
  }
}

/**
 * Cancel the feedback process
 * @param {object} ctx - Telegram context
 */
async function cancelSuggestion(ctx) {
  try {
    const userId = ctx.from.id;
    const session = await getSession(userId);
    
    if (!session) {
      await ctx.answerCbQuery('حدث خطأ في الجلسة.');
      return;
    }
    
    // Delete the message
    if (session.feedbackMessageId) {
      try {
        await ctx.telegram.deleteMessage(ctx.chat.id, session.feedbackMessageId);
      } catch (error) {
        console.log(`Could not delete feedback message: ${error.message}`);
      }
    }
    
    // Reset session state
    await updateSession(userId, { 
      state: null,
      feedbackMessageId: null,
      feedbackRating: null
    });
    
    // Confirm cancellation
    await ctx.answerCbQuery('تم إلغاء التقييم.');
    
  } catch (error) {
    console.error('Error cancelling feedback:', error);
    await ctx.answerCbQuery('حدث خطأ أثناء إلغاء التقييم');
  }
}

/**
 * Send a formatted question message to the user
 * @param {object} ctx - Telegram context
 * @param {object} question - Question object
 * @param {number} totalQuestions - Total number of questions
 * @param {number} questionIndex - Current question index
 * @param {object} session - User session
 */
async function sendQuestionMessage(ctx, question, totalQuestions, questionIndex, session) {
    const questionNum = questionIndex + 1;
  let questionText = `<b>السؤال ${questionNum} من ${totalQuestions}:</b>\n\n${escapeHtml(question.question)}\n`;
    
    // Create appropriate keyboard based on question type
    let keyboard;
    
    if (session.type === 'MCQ') {
    // Validate that the question has options
    if (!question.options || !Array.isArray(question.options) || question.options.length < 2) {
      console.error(`Invalid MCQ question at index ${questionIndex}: missing or invalid options array`);
      await ctx.reply('حدث خطأ في تنسيق السؤال. يرجى التواصل مع مدير البوت.');
      return;
    }
    
    // Check that there are at least 2 options and no more than 4
    const optionsCount = Math.min(Math.max(question.options.length, 2), 4);
    
    // Add options to the question text
    const options = question.options.slice(0, optionsCount).map((opt, i) => {
      // Remove any existing letter prefixes (like "A)", "B) ", etc.) from the options
      // Use a more specific regex to ensure we don't remove the first letter of the actual content
      let cleanedOption = opt.replace(/^([A-D][\)\.\:]|\([A-D]\))\s+/, '');
      return `${String.fromCharCode(65 + i)}) ${escapeHtml(cleanedOption)}`;
    }).join('\n');
    
    questionText += `\n${options}`;
    
    // Create option buttons (A, B, C, D) based on available options
    const keyboardRows = [];
    
    // Create first row with options 0 and 1 (if available)
    const firstRow = question.options.slice(0, 2).map((_, i) => ({ 
            text: String.fromCharCode(65 + i), 
            callback_data: `answer_${questionIndex}_${String.fromCharCode(65 + i)}` 
    }));
    
    if (firstRow.length > 0) {
      keyboardRows.push(firstRow);
    }
    
    // Create second row with options 2 and 3 (if available)
    if (optionsCount > 2) {
      const secondRow = question.options.slice(2, 4).map((_, i) => ({ 
            text: String.fromCharCode(65 + i + 2), 
            callback_data: `answer_${questionIndex}_${String.fromCharCode(65 + i + 2)}` 
      }));
      
      if (secondRow.length > 0) {
        keyboardRows.push(secondRow);
      }
    }
    
    keyboard = {
      inline_keyboard: keyboardRows
      };
    } else if (session.type === 'TF') {
      // Create True/False buttons
      keyboard = {
        inline_keyboard: [
          [
            { text: 'صح ✓', callback_data: `answer_${questionIndex}_true` },
            { text: 'خطأ ✗', callback_data: `answer_${questionIndex}_false` }
          ]
        ]
      };
    }
    
  // Send the new question message
  try {
        const sentMessage = await ctx.reply(questionText, { 
          parse_mode: 'HTML',
          reply_markup: keyboard 
        });
        
    // Store the message ID without waiting
    updateSession(ctx.from.id, {
      activeQuestionMessageId: sentMessage.message_id,
      currentQuestionIndex: questionIndex
    });
    
    return sentMessage;
  } catch (error) {
    console.error('Error sending question message:', error);
    
    // Try sending a simplified message if there might be formatting issues
    return await ctx.reply(`السؤال ${questionNum} من ${totalQuestions}: ${question.question}`, { 
        reply_markup: keyboard 
      });
  }
}

/**
 * Handle next question button click
 * @param {object} ctx - Telegram context
 */
async function handleNextQuestion(ctx) {
  try {
    // Extract next question index from the callback data
    const userId = ctx.from.id;
    const match = ctx.callbackQuery.data.match(/^next_question_(\d+)$/);
    if (!match) {
      console.error('Invalid next_question callback data');
      await ctx.answerCbQuery('حدث خطأ. يرجى المحاولة مرة أخرى.');
      return;
    }
    
    const nextQuestionIndex = parseInt(match[1], 10);
    logger.info(`User ${userId} requested next question: ${nextQuestionIndex}`);
    
    // Get a fresh copy of the session
    const session = await getSession(userId);
    
    if (!session || !session.questions || !Array.isArray(session.questions)) {
      logger.error(`No valid session or questions found for user ${userId}`);
      await ctx.answerCbQuery('حدث خطأ. يرجى إعادة تشغيل الاختبار.');
      
      // Return to main menu as a fallback
      await ctx.editMessageText('حدث خطأ في الاختبار. يرجى إعادة المحاولة.', {
        reply_markup: {
          inline_keyboard: [
            [{ text: 'العودة إلى القائمة الرئيسية 🏠', callback_data: 'main_menu' }]
          ]
        }
      });
      return;
    }
    
    logger.debug(`Current session state for user ${userId}:`, {
      type: session.type,
      currentQuestionIndex: session.currentQuestionIndex,
      questionsCount: session.questions.length,
      requestedNextQuestion: nextQuestionIndex
    });
    
    // Validate the requested question index
    if (nextQuestionIndex < 0 || nextQuestionIndex >= session.questions.length) {
      logger.error(`Invalid question index ${nextQuestionIndex} for user ${userId}. Total questions: ${session.questions.length}`);
      
      // Correct the index if possible
      const correctedIndex = Math.min(Math.max(0, nextQuestionIndex), session.questions.length - 1);
      logger.info(`Corrected question index to ${correctedIndex}`);
      
      // Use the corrected index
      await startInteractiveTest(ctx, session.questions, session, correctedIndex);
      return;
    }
    
    // Clean up the previous messages if they exist
    await cleanupPreviousMessages(ctx, session);
    
    // Update session with the next question index and clear message IDs
    await updateSession(userId, {
      currentQuestionIndex: nextQuestionIndex,
      activeQuestionMessageId: null,
      feedbackMessageId: null
    });
    
    // Start the test at the new question index
    await startInteractiveTest(ctx, session.questions, session, nextQuestionIndex);
  } catch (error) {
    logger.error(`Error in handleNextQuestion: ${error.message}`);
    
    try {
      // Try to gracefully recover and show a message to the user
      await ctx.answerCbQuery('حدث خطأ. يرجى المحاولة مرة أخرى.');
      
      // Show error message with main menu option
      await ctx.editMessageText('حدث خطأ أثناء تحميل السؤال التالي. يرجى المحاولة مرة أخرى.', {
        reply_markup: {
          inline_keyboard: [
            [{ text: 'إعادة المحاولة', callback_data: 'take_interactive_test' }],
            [{ text: 'العودة إلى القائمة الرئيسية 🏠', callback_data: 'main_menu' }]
          ]
        }
      });
    } catch (recoveryError) {
      // Last resort - just log the error
      logger.error(`Failed to recover from error: ${recoveryError.message}`);
    }
  }
}

/**
 * Show question answers to the user
 * @param {object} ctx - Telegram context
 */
async function showAnswers(ctx) {
  try {
    console.log(`User ${ctx.from.id} requested to show answers`);
    
    const userId = ctx.from.id;
    const session = getSession(userId);
    
    if (!session || !session.questions || session.questions.length === 0) {
      await ctx.reply('عذراً، لم يتم العثور على أسئلة. يرجى إنشاء أسئلة جديدة.');
      return;
    }
    
    // Send questions with answers
    await sendQuestions(ctx, session.questions, session);
    
    // Update callback
    await ctx.answerCbQuery('تم إرسال الأسئلة مع الإجابات');
  } catch (error) {
    console.error('Error handling show_answers action:', error);
    await ctx.reply('حدث خطأ أثناء إرسال الأسئلة. يرجى المحاولة مرة أخرى.');
  }
}

/**
 * Handle user's answer to a question
 * @param {object} ctx - Telegram context
 * @param {number} questionIndex - Question index
 * @param {string} userAnswer - User's answer
 */
async function handleUserAnswer(ctx, questionIndex, userAnswer) {
  try {
    const userId = ctx.from.id;
    const session = getSession(userId);
    
    if (!session || !session.questions) {
      await ctx.answerCbQuery('حدث خطأ في الجلسة. يرجى بدء الاختبار من جديد.');
      return;
    }
    
    const question = session.questions[questionIndex];
    if (!question) {
      await ctx.answerCbQuery('سؤال غير موجود. يرجى تجربة سؤال آخر.');
      return;
    }
    
    logger.user(`Handling answer from user ${userId} for question ${questionIndex}: ${userAnswer}`);
    logger.question(`Question data for index ${questionIndex}:`, question);
    
    // Get current quiz score
    const session2 = getSession(userId);
    const quizScore = session2.quizScore || {
      correct: 0,
      incorrect: 0,
      total: 0,
      answers: []
    };
    
    // Make sure answers array exists
    if (!quizScore.answers) {
      quizScore.answers = [];
    }
    
    // Check if answer is correct based on question type
    let isCorrect = false;
    let correctAnswer = '';
    
    if (session.type === 'MCQ') {
      // Convert letter answer (A, B, C, D) to index (0, 1, 2, 3)
      const answerIndex = userAnswer.charCodeAt(0) - 65;
      
      // Determine the correct answer based on the question format
      let correctIndex = -1;
      
      if ('correctOption' in question) {
        // Format: { correctOption: 2 }
        correctIndex = parseInt(question.correctOption);
        correctAnswer = String.fromCharCode(65 + correctIndex);
      } else if ('correctAnswer' in question) {
        // Format: { correctAnswer: 'C' }
        if (/^[A-D]$/.test(question.correctAnswer)) {
          correctIndex = question.correctAnswer.charCodeAt(0) - 65;
          correctAnswer = question.correctAnswer;
        } else if (typeof question.correctAnswer === 'number') {
          correctIndex = question.correctAnswer;
          correctAnswer = String.fromCharCode(65 + correctIndex);
        }
      } else if ('answer' in question && typeof question.answer === 'number') {
        // Format: { answer: 2 }
        correctIndex = question.answer;
        correctAnswer = String.fromCharCode(65 + correctIndex);
      } else if ('answer' in question && typeof question.answer === 'string') {
        // Check if the answer is a letter (A, B, C, D)
        if (/^[A-D]$/.test(question.answer)) {
          // Format: { answer: 'C' }
          correctIndex = question.answer.charCodeAt(0) - 65;
          correctAnswer = question.answer;
        } else {
          // Format: { answer: 'Some text' } - need to find which option matches
          const optionIndex = question.options.findIndex(opt => opt === question.answer);
          if (optionIndex !== -1) {
            correctIndex = optionIndex;
            correctAnswer = String.fromCharCode(65 + optionIndex);
          } else {
            // If we can't find the exact match, let's use the first option as a fallback
            correctIndex = 0;
            correctAnswer = 'A';
            logger.debug(`Could not find option matching answer text "${question.answer}"`);
          }
        }
      } else {
        // If we cannot determine the answer format, default to using the first option
        logger.error(`Unknown MCQ answer format for question ${questionIndex}, defaulting to answer A`);
        correctIndex = 0;
        correctAnswer = 'A';
      }
      
      isCorrect = answerIndex === correctIndex;
      
      // For display purposes, get the text of the correct answer
      let correctAnswerText = '';
      if (correctIndex >= 0 && correctIndex < question.options.length) {
        correctAnswerText = question.options[correctIndex];
      }
      
      logger.question(`MCQ: User answered ${userAnswer} (index ${answerIndex}), correct answer is ${correctAnswer} (index ${correctIndex})`);
    } else if (session.type === 'TF') {
      // Convert user answer to boolean
      const userBool = userAnswer.toLowerCase() === 'true';
      
      // Determine correct answer based on the question format
      let correctBool = false;
      
      if ('isTrue' in question) {
        // Format: { isTrue: true/false }
        correctBool = question.isTrue;
      } else if ('answer' in question && typeof question.answer === 'boolean') {
        // Format: { answer: true/false }
        correctBool = question.answer;
      } else if ('answer' in question && ['true', 'false'].includes(String(question.answer).toLowerCase())) {
        // Format: { answer: 'true'/'false' }
        correctBool = String(question.answer).toLowerCase() === 'true';
      } else {
        logger.error(`Unknown TF answer format for question ${questionIndex}`);
        await ctx.answerCbQuery('حدث خطأ في تنسيق السؤال');
        return;
      }
      
      isCorrect = userBool === correctBool;
      correctAnswer = correctBool ? 'صح' : 'خطأ';
      
      logger.question(`TF: User answered ${userAnswer} (${userBool}), correct answer is ${correctAnswer} (${correctBool})`);
    }
    
    // Update score
    if (isCorrect) {
      quizScore.correct++;
    } else {
      quizScore.incorrect++;
    }
    quizScore.total++;
    
    // Store the user's answer for reporting
    quizScore.answers.push({
      questionIndex,
      question: question.question,
      userAnswer,
      correctAnswer,
      isCorrect,
      explanation: question.explanation || null
    });
    
    // Update session with new score
    updateSession(userId, { quizScore });
    
    // Prepare feedback message
    let feedbackMsg = '';
    if (isCorrect) {
      feedbackMsg = `✅ <b>إجابة صحيحة!</b>\n\n`;
    } else {
      // For MCQ, get the full text of the correct answer
      let correctAnswerDisplay = '';
      if (session.type === 'MCQ') {
        // Find the correct option text
        let correctOptionText = '';
        if ('correctOption' in question && question.options[question.correctOption]) {
          correctOptionText = question.options[question.correctOption];
        } else if ('answer' in question && typeof question.answer === 'number' && question.options[question.answer]) {
          correctOptionText = question.options[question.answer];
        } else if ('answer' in question && typeof question.answer === 'string') {
          if (/^[A-D]$/.test(question.answer)) {
            const index = question.answer.charCodeAt(0) - 65;
            if (question.options[index]) {
              correctOptionText = question.options[index];
            }
          } else {
            // If answer is the actual text, use it directly
            correctOptionText = question.answer;
          }
        }
        
        // Combine the letter and the text
        correctAnswerDisplay = `${correctAnswer}) ${escapeHtml(correctOptionText)}`;
      } else {
        // For TF, just show صح/خطأ
        correctAnswerDisplay = escapeHtml(correctAnswer);
      }
      
      feedbackMsg = `❌ <b>إجابة خاطئة!</b>\n\n` +
        `الإجابة الصحيحة هي: ${correctAnswerDisplay}\n\n`;
    }
    
    // Add explanation if available
    if (question.explanation) {
      feedbackMsg += `<b>التوضيح:</b> ${escapeHtml(arabicExplanation(cleanExplanationText(question.explanation)))}\n\n`;
    }
    
    // Truncate message if too long to avoid Telegram length limits
    feedbackMsg = truncateMessage(feedbackMsg, 4000);
    
    // Calculate next question index
    const nextQuestionIndex = questionIndex + 1;
    const hasMoreQuestions = nextQuestionIndex < session.questions.length;
    
    // Create keyboard for next action
    const keyboard = {
      inline_keyboard: [
        [
          hasMoreQuestions 
            ? { text: 'السؤال التالي ⬅️', callback_data: `next_question_${nextQuestionIndex}` }
            : { text: 'انتهاء الاختبار 🏁', callback_data: `next_question_${nextQuestionIndex}` }
        ]
      ]
    };
    
    // Send feedback message
    const sentMessage = await ctx.reply(feedbackMsg, {
      parse_mode: 'HTML',
      reply_markup: keyboard
    });
    
    // Store the feedback message ID for future deletion
    updateSession(userId, { feedbackMessageId: sentMessage.message_id });
    
    // Answer the callback query
    await ctx.answerCbQuery(isCorrect ? 'إجابة صحيحة! 👏' : 'إجابة خاطئة! 😓');
    
  } catch (error) {
    logger.error(`Error handling user answer: ${error.message}`);
    logger.error(`Error stack: ${error.stack}`);
    await ctx.answerCbQuery('حدث خطأ أثناء معالجة إجابتك');
    
    try {
      const userId = ctx.from.id;
      // Even in case of error, try to show the next question
      const nextQuestionIndex = parseInt(questionIndex) + 1;
      const currentSession = getSession(userId);
      
      if (currentSession && currentSession.questions && 
          nextQuestionIndex < currentSession.questions.length) {
        logger.debug(`Attempting to recover by showing next question ${nextQuestionIndex} after error`);
        // Force update session state
        updateSession(userId, { 
          currentQuestionIndex: nextQuestionIndex,
          activeQuestionMessageId: null,
          feedbackMessageId: null 
        });
        
        // Wait a moment then try to show the next question
        setTimeout(async () => {
          try {
            const updatedSession = getSession(userId);
            await startInteractiveTest(ctx, updatedSession.questions, updatedSession, nextQuestionIndex);
          } catch (e) {
            logger.error(`Error in recovery attempt: ${e.message}`);
          }
        }, 1000);
      }

      await ctx.reply('تم تسجيل إجابتك. سنتابع مع السؤال التالي.');
    } catch (replyError) {
      logger.error(`Could not send error reply: ${replyError.message}`);
    }
  }
}

/**
 * Get MCQ or TF questions from GPT API
 * @param {object} ctx - Telegram context object
 * @param {string} topic - Topic to generate questions for 
 * @param {string} type - 'MCQ' or 'TF'
 * @param {number} count - Number of questions to generate
 */
async function getFromGPT(ctx, topic, type, count) {
  try {
    const userId = ctx.from.id;
    // Create a basic session right away without waiting
    initializeUserSession(userId, type);
    
    // Send loading message
    const loadingMessage = await ctx.reply('جاري إنشاء الأسئلة... ⏳');
    
    // Get questions from API service
    let questions = [];
    try {
      questions = await apiService.generateQuestionsFromAPI(topic, type, count);
    } catch (apiError) {
      console.error('Error generating questions from API:', apiError);
            await ctx.telegram.editMessageText(
              ctx.chat.id,
        loadingMessage.message_id,
              null,
        'فشل في إنشاء الأسئلة. يرجى المحاولة مرة أخرى. 🙁'
      );
      return;
    }
    
    if (!questions || questions.length === 0) {
      await ctx.telegram.editMessageText(
        ctx.chat.id, 
        loadingMessage.message_id,
        null,
        'لم نتمكن من إنشاء أسئلة بناءً على هذا الموضوع. يرجى تجربة موضوع مختلف. 🙁'
      );
      return;
    }
    
    // Update session with questions
    const session = getSession(userId) || {};
    session.questions = questions;
    session.type = type;
    session.topic = topic;
    session.currentQuestionIndex = 0;
    
    // Clear any previous quiz data
    if (session.quizScore) {
      session.quizScore = {
        correct: 0,
        incorrect: 0,
        total: 0,
        answers: []
      };
    }
    
    // Save session
    saveSession(userId, session);
    
    // Update loading message to show success
        await ctx.telegram.editMessageText(
          ctx.chat.id,
      loadingMessage.message_id,
          null,
      `تم إنشاء ${questions.length} سؤال حول "${topic}" بنجاح! 🎉`,
          {
            reply_markup: {
              inline_keyboard: [
                [
              { text: 'بدء الاختبار 🚀', callback_data: 'take_interactive_test' },
              { text: 'عرض الأسئلة 📝', callback_data: 'show_answers' }
                ]
              ]
            }
          }
        );
      } catch (error) {
    console.error('Error in getFromGPT:', error);
    await ctx.reply('حدث خطأ أثناء إنشاء الأسئلة. يرجى المحاولة مرة أخرى.');
  }
}

/**
 * Initialize a new user session
 * @param {string|number} userId - User ID
 * @param {string} type - Question type
 */
function initializeUserSession(userId, type) {
  const existingSession = getSession(userId);
  
  // If session already exists, don't overwrite it completely
  if (existingSession) {
    updateSession(userId, { type });
    return;
  }
  
  // Create a new session with minimal data first
  const session = {
    type,
    questions: [],
    currentQuestionIndex: 0,
    quizScore: {
      correct: 0,
      incorrect: 0,
      total: 0,
      answers: []
    }
  };
  
  saveSession(userId, session);
}

/**
 * Handle /start command
 * @param {object} ctx - Telegram context
 */
async function handleStart(ctx) {
  try {
    const userId = ctx.from.id;
    console.log(`User ${userId} started the bot`);
    
    // Initialize user session
    initializeUserSession(ctx);
    
    // Welcome message
    return await ctx.reply('مرحباً بك في بوت انشاء الأسئلة! 🤖\nالرجاء استخدام القائمة الرئيسية لاختيار نوع الأسئلة.', {
          reply_markup: {
            inline_keyboard: [
              [
            { text: '📝 اسئلة خيارات متعددة (MCQ)', callback_data: 'MCQ' },
            { text: '✅ أسئلة صح/خطأ (TF)', callback_data: 'TF' }
              ],
              [
                { text: '❓ كيفية عمل البوت', callback_data: 'how_bot_works' }
              ]
            ]
          }
        });
  } catch (error) {
    console.error('Error in handleStart:', error);
    await ctx.reply('عذراً، حدث خطأ ما. يرجى المحاولة مرة أخرى لاحقاً.');
  }
}

/**
 * Handle /help command
 * @param {object} ctx - Telegram context
 */
async function handleHelp(ctx) {
  try {
    const helpText = `
<b>🤖 مرحباً بك في بوت انشاء الأسئلة!</b>

<b>الأوامر المتاحة:</b>
/start - بدء البوت
/help - عرض هذه المساعدة

<b>أنواع الأسئلة:</b>
📝 <b>أسئلة اختيار من متعدد (MCQ)</b>: أسئلة مع خيارات متعددة
✅ <b>أسئلة صح/خطأ (TF)</b>: أسئلة يكون جوابها إما صح أو خطأ

<b>كيف تستخدم البوت:</b>
1. اضغط على نوع الأسئلة الذي تريده
2. حدد عدد الأسئلة التي تريدها
3. أجب على الأسئلة بالضغط على الخيار الصحيح
4. شاهد نتيجتك في نهاية الاختبار
5. شاركنا رأيك في الاختبار للمساعدة في تحسين البوت

للتواصل مع مدير البوت، يرجى استخدام @sytus
`;

    return await ctx.reply(helpText, {
        parse_mode: 'HTML',
        reply_markup: {
          inline_keyboard: [
            [
            { text: '📝 اسئلة خيارات متعددة (MCQ)', callback_data: 'MCQ' },
            { text: '✅ أسئلة صح/خطأ (TF)', callback_data: 'TF' }
            ],
            [
              { text: '❓ كيفية عمل البوت', callback_data: 'how_bot_works' }
            ]
          ]
        }
      });
  } catch (error) {
    console.error('Error in handleHelp:', error);
    await ctx.reply('عذراً، حدث خطأ ما. يرجى المحاولة مرة أخرى لاحقاً.');
  }
}

/**
 * Convert English explanation to Arabic or remove English parts
 * @param {string} explanation - The explanation text
 * @returns {string} Arabic-only explanation
 */
function arabicExplanation(explanation) {
  // If explanation already contains Arabic (starts with Arabic characters)
  if (/[\u0600-\u06FF]/.test(explanation.substring(0, 20))) {
    return explanation;
  }
  
  // Remove common English prefixes
  let cleaned = explanation
    .replace(/^(True|False)\.?\s*/i, '')
    .replace(/^This statement is (true|false)\.?\s*/i, '')
    .replace(/^The statement is (true|false)\.?\s*/i, '');
  
  // If explanation contains a period followed by Arabic text, keep only Arabic
  const arabicMatch = cleaned.match(/.*?\.\s*([\u0600-\u06FF].*)/);
  if (arabicMatch && arabicMatch[1]) {
    return arabicMatch[1];
  }
  
  return cleaned;
}

/**
 * Truncate a message to fit Telegram's limits while preserving important information
 * @param {string} message The message to truncate
 * @param {number} maxLength Maximum length (default: 4000)
 * @returns {string} Truncated message
 */
function truncateMessage(message, maxLength = 4000) {
  if (message.length <= maxLength) return message;
  
  // Split into lines to preserve formatting
  const lines = message.split('\n');
  let truncatedMessage = '';
  let currentLength = 0;
  
  for (const line of lines) {
    // If adding this line would exceed the limit, stop
    if (currentLength + line.length + 1 > maxLength) {
      // Add ellipsis and return
      truncatedMessage += '\n... (message truncated)';
      break;
    }
    
    truncatedMessage += line + '\n';
    currentLength += line.length + 1;
  }
  
  return truncatedMessage.trim();
}

/**
 * Generate default questions when API fails
 * @param {string} textSample - Sample of the text content
 * @param {string} type - Question type (MCQ or TF)
 * @param {number} count - Number of questions to generate
 * @returns {Array} Array of question objects
 */
function generateDefaultQuestions(textSample, type, count = 5) {
  const questions = [];
  
  // Try to detect the general subject based on keywords
  const lowerText = textSample.toLowerCase();
  
  // Extract important phrases/sentences from the text
  const sentences = textSample.split(/[.!?]/).filter(s => s.trim().length > 20);
  const importantPhrases = sentences.slice(0, Math.min(10, sentences.length));
  
  // Extract key terms (longer words that might be important)
  const words = textSample.match(/\b[A-Za-z]{5,15}\b/g) || [];
  const uniqueWords = [...new Set(words)];
  const keyTerms = uniqueWords.slice(0, Math.min(20, uniqueWords.length));
  
  // Simple subject detection
  let subject = 'general knowledge';
  if (lowerText.includes('math') || lowerText.includes('equation') || lowerText.includes('calculate')) {
    subject = 'mathematics';
  } else if (lowerText.includes('history') || lowerText.includes('century') || lowerText.includes('war')) {
    subject = 'history';
  } else if (lowerText.includes('science') || lowerText.includes('biology') || lowerText.includes('chemistry')) {
    subject = 'science';
  } else if (lowerText.includes('geography') || lowerText.includes('country') || lowerText.includes('capital')) {
    subject = 'geography';
  } else if (lowerText.includes('literature') || lowerText.includes('book') || lowerText.includes('author')) {
    subject = 'literature';
  }
  
  // Create questions based on the actual text content
  if (type === 'MCQ') {
    // Multiple choice questions using actual content
    for (let i = 0; i < Math.min(count, importantPhrases.length); i++) {
      const phrase = importantPhrases[i].trim();
      if (phrase.length < 20) continue; // Skip short phrases
      
      // Make a question from the phrase
      const question = `Which statement is related to: "${phrase.substring(0, 70)}..."?`;
      
      // Create options using other phrases and key terms
      const correctOption = `This relates to ${subject} concepts mentioned in the text.`;
      const otherPhrases = importantPhrases.filter((p, index) => index !== i);
      
      // Mix in some key terms for the options
      const options = [
        correctOption,
        keyTerms.length > 0 ? `This contradicts the definition of ${keyTerms[0]} in the text.` : "This contradicts the main point of the text.",
        otherPhrases.length > 0 ? `This refers to ${otherPhrases[0].substring(0, 30)}...` : "This is unrelated to the text content.",
        `This is not mentioned anywhere in the text.`
      ];
      
      // Shuffle options
      for (let j = options.length - 1; j > 0; j--) {
        const k = Math.floor(Math.random() * (j + 1));
        [options[j], options[k]] = [options[k], options[j]];
      }
      
      // Find the correct answer
      const answerIndex = options.indexOf(correctOption);
      const answerLetter = String.fromCharCode(65 + answerIndex);
      
      questions.push({
        question: question,
        options: options,
        answer: answerLetter,
        explanation: `This question is based on your text content about ${subject}.`
      });
    }
  } else {
    // True/False questions using actual content
    for (let i = 0; i < Math.min(count, importantPhrases.length); i++) {
      const phrase = importantPhrases[i].trim();
      if (phrase.length < 20) continue; // Skip short phrases
      
      // Decide if this will be a true or false question
      const isTrue = Math.random() > 0.5;
      
      let question;
      if (isTrue) {
        question = `The following statement appears in the text: "${phrase.substring(0, 70)}..."`;
      } else {
        // Generate a false statement by modifying the phrase
        const modifiedPhrase = phrase.replace(
          /\b(is|are|was|were|has|have|do|does|did|can|could|will|would|should|may|might)\b/g,
          match => {
            const negations = {
              'is': 'is not', 'are': 'are not', 'was': 'was not', 'were': 'were not',
              'has': 'does not have', 'have': 'do not have', 'do': 'do not', 
              'does': 'does not', 'did': 'did not', 'can': 'cannot',
              'could': 'could not', 'will': 'will not', 'would': 'would not',
              'should': 'should not', 'may': 'may not', 'might': 'might not'
            };
            return negations[match] || match;
          }
        );
        question = `The following statement appears in the text: "${modifiedPhrase.substring(0, 70)}..."`;
      }
      
      questions.push({
        question: question,
        answer: isTrue ? 'True' : 'False',
        isTrue: isTrue,
        explanation: `This question is based on your text content about ${subject}.`
      });
    }
  }
  
  // If we couldn't generate enough questions from the text, add some generic ones
  const genericSubjectQuestions = getGenericSubjectQuestions(subject, type);
  while (questions.length < count) {
    if (genericSubjectQuestions.length > 0) {
      questions.push(genericSubjectQuestions.shift());
    } else {
      break;
    }
  }
  
  return questions;
}

/**
 * Get generic questions about a subject
 * @param {string} subject - Subject area
 * @param {string} type - Question type (MCQ or TF)
 * @returns {Array} Array of generic questions
 */
function getGenericSubjectQuestions(subject, type) {
  if (type === 'MCQ') {
    return [
      {
        question: `Which of these is most related to ${subject}?`,
        options: getOptionsForSubject(subject, 'related'),
        answer: 'A',
        explanation: `This is a general ${subject} question.`
      },
      {
        question: `Which statement about ${subject} is correct?`,
        options: getOptionsForSubject(subject, 'facts'),
        answer: 'B',
        explanation: `This is a general ${subject} question.`
      },
      {
        question: `What is a key concept in ${subject}?`,
        options: getOptionsForSubject(subject, 'concepts'),
        answer: 'C',
        explanation: `This is a general ${subject} question.`
      }
    ];
  } else {
    return [
      {
        question: `${subject} is considered one of the oldest fields of study.`,
        answer: 'True',
        isTrue: true,
        explanation: `This is a general ${subject} question.`
      },
      {
        question: `${subject} has no practical applications in the modern world.`,
        answer: 'False',
        isTrue: false,
        explanation: `This is a general ${subject} question.`
      },
      {
        question: `Many famous scientists contributed to the field of ${subject}.`,
        answer: 'True',
        isTrue: true,
        explanation: `This is a general ${subject} question.`
      }
    ];
  }
}

/**
 * Get options for a subject based on category
 * @param {string} subject - Subject area
 * @param {string} category - Category of options
 * @returns {Array} Array of options
 */
function getOptionsForSubject(subject, category) {
  const optionsMap = {
    'mathematics': {
      'related': ['Equations', 'Poetry', 'Cooking', 'Swimming'],
      'facts': ['All triangles have 4 sides', 'The sum of angles in a triangle is 180 degrees', 'Pi equals exactly 3', 'Algebra was invented in 2010'],
      'concepts': ['Subtraction', 'Metaphor', 'Evaporation', 'Rhyming'],
      'terms': ['Algorithm', 'Stanza', 'Precipitation', 'Dribbling'],
      'applications': ['Building bridges', 'Writing sonnets', 'Training pets', 'Designing video games']
    },
    'history': {
      'related': ['Past events', 'Chemical reactions', 'Computer programming', 'Baking'],
      'facts': ['World War II ended in 1965', 'Ancient Egypt had pharaohs', 'The Roman Empire existed in the 19th century', 'The American Revolution was in 1776'],
      'concepts': ['Civilization', 'Photosynthesis', 'Binary code', 'Harmony'],
      'terms': ['Dynasty', 'Molecule', 'Function', 'Melody'],
      'applications': ['Understanding current politics', 'Creating chemical compounds', 'Building websites', 'Composing songs']
    },
    'science': {
      'related': ['Experimentation', 'Poetry', 'Ancient civilizations', 'Basketball'],
      'facts': ['The Earth is flat', 'Water is composed of hydrogen and oxygen', 'Humans have 3 lungs', 'Gravity pulls objects together'],
      'concepts': ['Hypothesis', 'Metaphor', 'Democracy', 'Offside rule'],
      'terms': ['Molecule', 'Sonnet', 'Monarchy', 'Free throw'],
      'applications': ['Medical research', 'Writing novels', 'Archaeological digs', 'Sports coaching']
    },
    'default': {
      'related': ['Research', 'Entertainment', 'Exercise', 'Cooking'],
      'facts': ['All books contain pictures', 'Knowledge requires learning', 'Education was invented in 2000', 'Reading is no longer necessary'],
      'concepts': ['Understanding', 'Confusion', 'Enjoyment', 'Frustration'],
      'terms': ['Facts', 'Fiction', 'Opinion', 'Evidence'],
      'applications': ['Everyday decisions', 'Professional work', 'Academic research', 'Personal hobbies']
    }
  };
  
  // Get options for the specified subject or use default
  const subjectOptions = optionsMap[subject] || optionsMap['default'];
  return subjectOptions[category] || subjectOptions['related'];
}

module.exports = {
  handleTextMessage,
  handlePhotoMessage,
  handleDocumentMessage,
  processContentAndGenerateQuestions,
  askQuestionPreference,
  sendQuestionsWithoutAnswers,
  getSession,
  updateSession,
  sendQuestions,
  startInteractiveTest,
  cleanupPreviousMessages,
  showQuizSummary,
  showDetailedReport,
  sendQuestionMessage,
  handleNextQuestion,
  showAnswers,
  cleanExplanationText,
  getFromGPT,
  initializeUserSession,
  handleStart,
  handleHelp,
  requestFeedback,
  handleFeedbackRating,
  requestSuggestion,
  handleSuggestionText,
  handleNoSuggestion,
  saveSession,
  deleteUserFiles,
  handleUserAnswer,
  cancelSuggestion,
  updateProcessingProgress,
  truncateMessage,
  cleanExtractedText
}; 