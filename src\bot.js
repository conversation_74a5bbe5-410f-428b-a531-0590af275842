// Load environment variables from .env file
require('dotenv').config();

const fs = require('fs');
const path = require('path');
const { Telegraf, Markup, session } = require('telegraf');
const database = require('./database/database');
const config = require('./config');
const { getSession, saveSession, updateSession } = require('./services/sessionService');
const messageHandlers = require('./handlers/messageHandlers');
const { message } = require('telegraf/filters');
const { storeUser, logUser, isAdmin } = require('./utils/userUtils');
const logger = require('./utils/logger');

// Import message handlers from messageHandlers.js
const {
  handleStart,
  handleHelp,
  handleMCQ,
  handleTF,
  handleCallback,
  handleNextQuestion,
  showAnswers,
  showDetailedReport,
  handleUserAnswer,
  requestFeedback,
  handleFeedbackRating,
  requestSuggestion,
  handleNoSuggestion,
  cancelSuggestion
} = require('./handlers/messageHandlers');

// Import command handlers from commandHandlers.js
const {
  handleStats,
  handleAdmin,
  handleKeys,
  handleUsers,
  handleFeedback
} = require('./handlers/commandHandlers');

// Import admin handlers
const {
  handleSystemStatus,
  handleBroadcast,
  handleUserManagement,
  handleAdminSettings,
  handleQuestionsPerPageSetting,
  handleImageQuestionsCountSetting,
  handleFilesPerDaySetting,
  handleCustomQuestionsInput,
  handleCustomFilesInput,
  handleHashNumberMessage,
  setQuestionsPerPage,
  setImageQuestionsCount,
  setFilesPerDay,
  saveSettingsToEnv,
  handleLogsReview,
  handleFeedbackJson,
  handleFeedbackJsonViewer,
  handleModelStatus,
  handleTestModels,
  handleUserJsonViewer,
  handleUserJson
} = require('./handlers/adminHandlers');

logger.info('Loading environment variables...');

// Create necessary directories
const dataDir = path.join(__dirname, '../data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
  logger.info('Created data directory');
}

// Initialize the bot
logger.info('Starting bot...');
const token = config.token;

if (!token) {
  logger.error('No bot token provided! Set it in .env file as TELEGRAM_BOT_TOKEN or TELEGRAM_TOKEN');
  process.exit(1);
}

logger.info('Configuration loaded.');

const bot = new Telegraf(process.env.TELEGRAM_BOT_TOKEN, {
  handlerTimeout: 90000, // 90 seconds timeout for handlers
  telegram: {
    // Remove any request throttling to allow concurrent processing
    apiRoot: process.env.TELEGRAM_API_URL || 'https://api.telegram.org',
    webhookReply: false // Don't wait for webhook responses
  }
});

// Use session middleware to store admin state
bot.use(session());

// Set up session middleware (optional)
bot.use(async (ctx, next) => {
  const start = Date.now();
  
  // Track user if available
  if (ctx.from) {
    await storeUser(ctx.from);
    await logUser(ctx.from.id, ctx.updateType || 'interaction');
  }
  
  // Don't wait for one request to finish before handling the next
  next().catch(error => {
    logger.error(`Error in middleware: ${error.message}`);
  });
  
  const ms = Date.now() - start;
  logger.debug(`Response time: ${ms}ms for ${ctx.updateType}`);
});

// Middleware to check if user has joined the required channel
bot.use(async (ctx, next) => {
  // Skip middleware for admin users
  if (ctx.from && config.admins.includes(ctx.from.id.toString())) {
    return next();
  }
  
  // Skip middleware if no channel ID is required
  if (!config.requiredChannelId) {
    return next();
  }
  
  // Skip if ctx.from is undefined (e.g., channel posts, service messages)
  if (!ctx.from) {
    logger.warn('Cannot check channel membership: ctx.from is undefined');
    return next();
  }
  
  try {
    // Skip for callback_query updates related to joining channel
    if (ctx.callbackQuery && ctx.callbackQuery.data === 'check_membership') {
      return next();
    }
    
    // Validate the channel ID before attempting to check membership
    let isValidChannel = true;
    try {
      // Try to get basic channel info first
      await ctx.telegram.getChat(config.requiredChannelId);
    } catch (chatError) {
      if (chatError.message.includes('chat not found') || 
          chatError.message.includes('Bad Request')) {
        logger.error(`Invalid channel ID ${config.requiredChannelId}: ${chatError.message}`);
        isValidChannel = false;
      }
    }
    
    // If channel is invalid, skip the membership check
    if (!isValidChannel) {
      logger.warn(`Skipping channel membership check for invalid channel: ${config.requiredChannelId}`);
      return next();
    }
    
    // Check user's membership in the channel
    const member = await ctx.telegram.getChatMember(
      config.requiredChannelId, 
      ctx.from.id
    );
    
    // List of statuses that indicate the user is a member of the channel
    const memberStatuses = ['creator', 'administrator', 'member'];
    
    if (memberStatuses.includes(member.status)) {
      // User is a member, proceed to next middleware
      return next();
    } else {
      // User is not a member, send a message with a button to join
      const channelInfo = await ctx.telegram.getChat(config.requiredChannelId);
      const channelName = channelInfo.title || 'our channel';
      const username = channelInfo.username ? `@${channelInfo.username}` : null;
      
      // Create invite link if channel doesn't have a username
      const inviteLink = username ? `https://t.me/${channelInfo.username}` : 
                                   await ctx.telegram.createChatInviteLink(config.requiredChannelId);
      
      // Send message with button to join the channel
      await ctx.reply(
        `👋 مرحباً! للاستفادة من خدمات البوت، يرجى الانضمام إلى ${channelName} أولاً.`,
        {
          reply_markup: {
            inline_keyboard: [
              [
                { 
                  text: `الانضمام إلى ${channelName}`, 
                  url: username ? `https://t.me/${channelInfo.username}` : inviteLink.invite_link
                }
              ],
              [
                { 
                  text: 'تحقق من العضوية', 
                  callback_data: 'check_membership'
                }
              ]
            ]
          }
        }
      );
      
      // Log the event
      logger.info(`User ${ctx.from.id} was prompted to join channel ${config.requiredChannelId}`);
      
      // Don't proceed to next middleware
      return;
    }
  } catch (error) {
    logger.error(`Error checking channel membership: ${error.message}`);
    // If there's an error checking membership, allow the user to proceed
    return next();
  }
});

// Initialize database
try {
  logger.info('Initializing database...');
  database.initDatabase();
} catch (err) {
  logger.error(`Failed to initialize database: ${err.message}`);
  process.exit(1);
}

// Set up command handlers
bot.command('start', async (ctx) => {
  await handleStart(ctx);
});

bot.command('help', async (ctx) => {
  await handleHelp(ctx);
});

bot.command('stats', async (ctx) => {
  try {
    // Check if user is admin
    const isUserAdmin = await isAdmin(ctx.from.id);
    if (!isUserAdmin) {
      return ctx.reply('You do not have permission to use this command.');
    }
    
    // Only call the handler if user is an admin
    await handleStats(ctx);
  } catch (error) {
    logger.error(`Error in /stats command: ${error.message}`);
    ctx.reply('An error occurred while processing your request.');
  }
});

bot.command('admin', async (ctx) => {
  try {
    // Check if user is admin
    const isUserAdmin = await isAdmin(ctx.from.id);
    if (!isUserAdmin) {
      return ctx.reply('You do not have permission to use this command.');
    }
    
    // Only call the handler if user is an admin
    await handleAdmin(ctx);
  } catch (error) {
    logger.error(`Error in /admin command: ${error.message}`);
    ctx.reply('An error occurred while processing your request.');
  }
});

bot.command('keys', async (ctx) => {
  try {
    // Check if user is admin
    const isUserAdmin = await isAdmin(ctx.from.id);
    if (!isUserAdmin) {
      return ctx.reply('You do not have permission to use this command.');
    }
    
    // Only call the handler if user is an admin
    await handleKeys(ctx);
  } catch (error) {
    logger.error(`Error in /keys command: ${error.message}`);
    ctx.reply('An error occurred while processing your request.');
  }
});

bot.command('users', async (ctx) => {
  try {
    // Check if user is admin
    const isUserAdmin = await isAdmin(ctx.from.id);
    if (!isUserAdmin) {
      return ctx.reply('You do not have permission to use this command.');
    }
    
    // Only call the handler if user is an admin
    await handleUsers(ctx);
  } catch (error) {
    logger.error(`Error in /users command: ${error.message}`);
    ctx.reply('An error occurred while processing your request.');
  }
});

bot.command('feedback', async (ctx) => {
  try {
    // Check if user is admin
    const isUserAdmin = await isAdmin(ctx.from.id);
    if (!isUserAdmin) {
      return ctx.reply('You do not have permission to use this command.');
    }
    
    // Only call the handler if user is an admin
    await handleFeedback(ctx);
  } catch (error) {
    logger.error(`Error in /feedback command: ${error.message}`);
    ctx.reply('An error occurred while processing your request.');
  }
});

// Add new admin commands
bot.command('system', async (ctx) => {
  try {
    // Check if user is admin
    const isUserAdmin = await isAdmin(ctx.from.id);
    if (!isUserAdmin) {
      return ctx.reply('You do not have permission to use this command.');
    }
    
    // Only call the handler if user is an admin
    await handleSystemStatus(ctx);
  } catch (error) {
    logger.error(`Error in /system command: ${error.message}`);
    ctx.reply('An error occurred while processing your request.');
  }
});

bot.command('broadcast', async (ctx) => {
  try {
    // Check if user is admin
    const isUserAdmin = await isAdmin(ctx.from.id);
    if (!isUserAdmin) {
      return ctx.reply('You do not have permission to use this command.');
    }
    
    // Only call the handler if user is an admin
    await handleBroadcast(ctx);
  } catch (error) {
    logger.error(`Error in /broadcast command: ${error.message}`);
    ctx.reply('An error occurred while processing your request.');
  }
});

bot.command('usermanage', async (ctx) => {
  try {
    // Check if user is admin
    const isUserAdmin = await isAdmin(ctx.from.id);
    if (!isUserAdmin) {
      return ctx.reply('You do not have permission to use this command.');
    }
    
    // Only call the handler if user is an admin
    await handleUserManagement(ctx);
  } catch (error) {
    logger.error(`Error in /usermanage command: ${error.message}`);
    ctx.reply('An error occurred while processing your request.');
  }
});

bot.command('amir', async (ctx) => {
  try {
    // Check if user is admin
    const isUserAdmin = await isAdmin(ctx.from.id);
    if (!isUserAdmin) {
      return ctx.reply('You do not have permission to use this command.');
    }
    
    // Only call the admin handler if user is an admin
    await handleAdminSettings(ctx);
  } catch (error) {
    logger.error(`Error in /amir command: ${error.message}`);
    ctx.reply('An error occurred while processing your request.');
  }
});

bot.command('logs', async (ctx) => {
  try {
    // Check if user is admin
    const isUserAdmin = await isAdmin(ctx.from.id);
    if (!isUserAdmin) {
      return ctx.reply('You do not have permission to use this command.');
    }
    
    // Only call the handler if user is an admin
    await handleLogsReview(ctx);
  } catch (error) {
    logger.error(`Error in /logs command: ${error.message}`);
    ctx.reply('An error occurred while processing your request.');
  }
});

bot.command('feedbackjson', async (ctx) => {
  try {
    // Check if user is admin
    const isUserAdmin = await isAdmin(ctx.from.id);
    if (!isUserAdmin) {
      return ctx.reply('You do not have permission to use this command.');
    }
    
    // Only call the handler if user is an admin
    await handleFeedbackJson(ctx);
  } catch (error) {
    logger.error(`Error in /feedbackjson command: ${error.message}`);
    ctx.reply('An error occurred while processing your request.');
  }
});

// Set up simple action handlers
bot.action('MCQ', (ctx) => {
  logger.user(`User ${ctx.from.id} selected MCQ`);
  const session = { type: 'MCQ', count: 15 }; // Always set to 15 questions
  saveSession(ctx.from.id, session);
  return ctx.reply('لقد اخترت أسئلة الاختيار من متعدد (MCQ). يرجى إرسال صورة أو مستند (PDF أو ملف نصي) وسأقوم بإنشاء أسئلة MCQ بناءً على المحتوى.');
});

bot.action('TF', (ctx) => {
  logger.user(`User ${ctx.from.id} selected TF`);
  const session = { type: 'TF', count: 15 }; // Always set to 15 questions
  saveSession(ctx.from.id, session);
  return ctx.reply('لقد اخترت أسئلة صح/خطأ (TF). يرجى إرسال صورة أو مستند (PDF أو ملف نصي) وسأقوم بإنشاء أسئلة صح/خطأ بناءً على المحتوى.');
});

// Handle membership check callback
bot.action('check_membership', async (ctx) => {
  try {
    // Skip if no channel is required
    if (!config.requiredChannelId) {
      await ctx.answerCbQuery('لا توجد قناة مطلوبة للانضمام');
      return ctx.reply('يمكنك استخدام البوت الآن. اختر نوع السؤال:', {
        reply_markup: {
          inline_keyboard: [
            [
              { text: '📝 اسئلة خيارات متعددة (MCQ)', callback_data: 'MCQ' },
              { text: '✅ أسئلة صح/خطأ (TF)', callback_data: 'TF' }
            ],
            [
              { text: '❓ كيفية عمل البوت', callback_data: 'how_bot_works' }
            ]
          ]
        }
      });
    }
    
    // Validate ctx.from exists before using it
    if (!ctx.from) {
      logger.error('Cannot check channel membership in callback: ctx.from is undefined');
      await ctx.answerCbQuery('حدث خطأ أثناء التحقق من العضوية');
      return ctx.reply('حدث خطأ أثناء التحقق من العضوية. يرجى المحاولة مرة أخرى لاحقاً.');
    }
    
    // Validate the channel ID before attempting to check membership
    let isValidChannel = true;
    let channelInfo = null;
    
    try {
      // Try to get basic channel info first
      channelInfo = await ctx.telegram.getChat(config.requiredChannelId);
    } catch (chatError) {
      if (chatError.message.includes('chat not found') || 
          chatError.message.includes('Bad Request')) {
        logger.error(`Invalid channel ID ${config.requiredChannelId}: ${chatError.message}`);
        isValidChannel = false;
      }
    }
    
    // If channel is invalid, skip the membership check and let user proceed
    if (!isValidChannel) {
      logger.warn(`Skipping channel membership check for invalid channel: ${config.requiredChannelId}`);
      await ctx.answerCbQuery('تم تخطي التحقق من العضوية');
      return ctx.reply('يمكنك استخدام البوت الآن. اختر نوع السؤال:', {
        reply_markup: {
          inline_keyboard: [
            [
              { text: '📝 اسئلة خيارات متعددة (MCQ)', callback_data: 'MCQ' },
              { text: '✅ أسئلة صح/خطأ (TF)', callback_data: 'TF' }
            ],
            [
              { text: '❓ كيفية عمل البوت', callback_data: 'how_bot_works' }
            ]
          ]
        }
      });
    }
    
    // Check user's membership in the channel
    const member = await ctx.telegram.getChatMember(
      config.requiredChannelId, 
      ctx.from.id
    );
    
    // List of statuses that indicate the user is a member of the channel
    const memberStatuses = ['creator', 'administrator', 'member'];
    
    if (memberStatuses.includes(member.status)) {
      // User is a member
      await ctx.answerCbQuery('تم التحقق من العضوية بنجاح!');
      await ctx.reply('شكراً للانضمام! يمكنك الآن استخدام البوت. اختر نوع السؤال:', {
        reply_markup: {
          inline_keyboard: [
            [
              { text: '📝 اسئلة خيارات متعددة (MCQ)', callback_data: 'MCQ' },
              { text: '✅ أسئلة صح/خطأ (TF)', callback_data: 'TF' }
            ],
            [
              { text: '❓ كيفية عمل البوت', callback_data: 'how_bot_works' }
            ]
          ]
        }
      });
    } else {
      // User is not a member
      const channelName = channelInfo.title || 'قناتنا';
      const username = channelInfo.username ? `@${channelInfo.username}` : null;
      
      // Create invite link if channel doesn't have a username
      const inviteLink = username ? `https://t.me/${channelInfo.username}` : 
                                   await ctx.telegram.createChatInviteLink(config.requiredChannelId);
      
      await ctx.answerCbQuery('لم يتم التحقق من العضوية، يرجى الانضمام إلى القناة أولاً', { show_alert: true });
      await ctx.reply(
        `يرجى الانضمام إلى ${channelName} للتمكن من استخدام البوت.`,
        {
          reply_markup: {
            inline_keyboard: [
              [
                { 
                  text: `الانضمام إلى ${channelName}`, 
                  url: username ? `https://t.me/${channelInfo.username}` : inviteLink.invite_link
                }
              ],
              [
                { 
                  text: 'تحقق من العضوية مرة أخرى', 
                  callback_data: 'check_membership'
                }
              ]
            ]
          }
        }
      );
    }
  } catch (error) {
    logger.error(`Error checking channel membership callback: ${error.message}`);
    await ctx.answerCbQuery('حدث خطأ أثناء التحقق من العضوية');
    await ctx.reply('حدث خطأ أثناء التحقق من العضوية. يرجى المحاولة مرة أخرى لاحقاً.');
  }
});

bot.action('main_menu', (ctx) => {
  logger.user(`User ${ctx.from.id} requested main menu`);
  return ctx.reply('اختر نوع السؤال:', {
    reply_markup: {
      inline_keyboard: [
        [
          { text: '📝 اسئلة خيارات متعددة (MCQ)', callback_data: 'MCQ' },
          { text: '✅ أسئلة صح/خطأ (TF)', callback_data: 'TF' }
        ],
        [
          { text: '❓ كيفية عمل البوت', callback_data: 'how_bot_works' }
        ]
      ]
    }
  });
});

// Register action handler for take_test (Update to redirect to interactive test)
bot.action('take_test', async (ctx) => {
  try {
    logger.user(`User ${ctx.from.id} requested to take a test`);
    const userId = ctx.from.id;
    const session = getSession(userId);
    
    if (!session || !session.questions || session.questions.length === 0) {
      await ctx.reply('عذراً، لم يتم العثور على أسئلة. يرجى إنشاء أسئلة جديدة.');
      return;
    }
    
    // Redirect to interactive test
    await messageHandlers.startInteractiveTest(ctx, session.questions, session, 0);
    
    // Update callback
    await ctx.answerCbQuery('بدء الاختبار التفاعلي');
  } catch (error) {
    logger.error(`Error handling take_test action: ${error.message}`);
    await ctx.reply('حدث خطأ أثناء إعداد الاختبار. يرجى المحاولة مرة أخرى.');
  }
});

bot.action('show_answers', async (ctx) => {
  try {
    logger.user(`User ${ctx.from.id} requested to show answers`);
    
    const userId = ctx.from.id;
    const session = getSession(userId);
    
    if (!session || !session.questions || session.questions.length === 0) {
      await ctx.reply('عذراً، لم يتم العثور على أسئلة. يرجى إنشاء أسئلة جديدة.');
      return;
    }
    
    // Send questions with answers
    await messageHandlers.sendQuestions(ctx, session.questions, session);
    
    // Update callback
    await ctx.answerCbQuery('تم إرسال الأسئلة مع الإجابات');
  } catch (error) {
    logger.error(`Error handling show_answers action: ${error.message}`);
    await ctx.reply('حدث خطأ أثناء إرسال الأسئلة. يرجى المحاولة مرة أخرى.');
  }
});

// Register action handler for take_interactive_test
bot.action('take_interactive_test', async (ctx) => {
  try {
    logger.user(`User ${ctx.from.id} requested interactive test`);
    const userId = ctx.from.id;
    const session = getSession(userId);
    
    if (!session || !session.questions || session.questions.length === 0) {
      await ctx.reply('عذراً، لم يتم العثور على أسئلة. يرجى إنشاء أسئلة جديدة.');
      return;
    }
    
    // Start interactive test with first question
    await messageHandlers.startInteractiveTest(ctx, session.questions, session, 0);
    
    // Update callback
    await ctx.answerCbQuery('بدء الاختبار التفاعلي');
  } catch (error) {
    logger.error(`Error handling take_interactive_test action: ${error.message}`);
    await ctx.reply('حدث خطأ أثناء إعداد الاختبار التفاعلي. يرجى المحاولة مرة أخرى.');
  }
});

// Handle action for user answer selection
bot.action(/answer_(\d+)_(.+)/, async (ctx) => {
  try {
    const questionIndex = parseInt(ctx.match[1], 10);
    const userAnswer = ctx.match[2];
    const userId = ctx.from.id;
    
    logger.user(`User ${userId} answered question ${questionIndex} with: ${userAnswer}`);
    
    // Get session and validate it exists
    const session = getSession(userId);
    if (!session) {
      logger.error(`No session found for user ${userId}`);
      await ctx.answerCbQuery('حدث خطأ في الجلسة. يرجى بدء اختبار جديد.');
      return;
    }
    
    // Remove the inline keyboard from the question message
    if (session.activeQuestionMessageId) {
      try {
        await ctx.telegram.editMessageReplyMarkup(
          ctx.chat.id,
          session.activeQuestionMessageId,
          undefined,
          { inline_keyboard: [] }
        );
        logger.debug(`Removed keyboard from question message ID: ${session.activeQuestionMessageId}`);
      } catch (error) {
        logger.error(`Could not remove keyboard: ${error.message}`);
      }
    }
    
    // Process the user's answer
    await messageHandlers.handleUserAnswer(ctx, questionIndex, userAnswer);
    
  } catch (error) {
    logger.error(`Error in answer handler: ${error.message}`);
    logger.error(`Error stack: ${error.stack}`);
    await ctx.answerCbQuery('حدث خطأ أثناء معالجة إجابتك');
    try {
      await ctx.reply('حدث خطأ أثناء معالجة إجابتك. يرجى المحاولة مرة أخرى أو بدء اختبار جديد.');
    } catch (replyError) {
      logger.error(`Could not send error reply: ${replyError.message}`);
    }
  }
});

// Handle action for "Next Question" button
bot.action(/next_question_(\d+)/, async (ctx) => {
  try {
    // Extract next question index from the callback data
    const userId = ctx.from.id;
    const nextQuestionIndex = parseInt(ctx.match[1], 10);
    
    logger.user(`User ${userId} requested next question: ${nextQuestionIndex}`);
    
    // Get a fresh copy of the session
    const session = getSession(userId);
    
    if (!session || !session.questions || !Array.isArray(session.questions)) {
      logger.error(`No valid session or questions found for user ${userId}`);
      await ctx.answerCbQuery('حدث خطأ. يرجى إعادة تشغيل الاختبار.');
      return;
    }
    
    logger.debug(`Current session state for user ${userId}:`, JSON.stringify({
      type: session.type,
      currentQuestionIndex: session.currentQuestionIndex,
      questionsCount: session.questions.length,
      requestedNextQuestion: nextQuestionIndex
    }));
    
    // Clean up the previous messages if they exist
    
    // 1. Delete the previous question message
    if (session.activeQuestionMessageId) {
      try {
        await ctx.telegram.deleteMessage(ctx.chat.id, session.activeQuestionMessageId);
        logger.debug(`Deleted previous question message ID: ${session.activeQuestionMessageId}`);
      } catch (error) {
        logger.error(`Could not delete previous question message: ${error.message}`);
      }
    }
    
    // 2. Delete the previous feedback message
    if (session.feedbackMessageId) {
      try {
        await ctx.telegram.deleteMessage(ctx.chat.id, session.feedbackMessageId);
        logger.debug(`Deleted previous feedback message ID: ${session.feedbackMessageId}`);
      } catch (error) {
        logger.error(`Could not delete feedback message: ${error.message}`);
      }
    }
    
    // Update session with the next question index and clear message IDs
    updateSession(userId, {
      currentQuestionIndex: nextQuestionIndex,
      activeQuestionMessageId: null,
      feedbackMessageId: null
    });
    
    // Get the questions array from the session
    const { questions } = session;
    
    // Check if next question index is valid - if not, we should still call startInteractiveTest
    // as it handles the end-of-questions logic and shows the summary
    if (nextQuestionIndex >= session.questions.length) {
      logger.debug(`Next question index ${nextQuestionIndex} exceeds available questions (${session.questions.length}) - showing summary`);
      // Still call startInteractiveTest with the final index to show the summary
      await messageHandlers.startInteractiveTest(ctx, questions, session, nextQuestionIndex);
      await ctx.answerCbQuery('انتهى الاختبار. عرض النتائج...');
      return;
    }
    
    // Start the interactive test with the next question
    await messageHandlers.startInteractiveTest(ctx, questions, session, nextQuestionIndex);
    
    // Confirm the callback query
    await ctx.answerCbQuery(`عرض السؤال ${nextQuestionIndex + 1}`);
    
  } catch (error) {
    logger.error(`Error in next_question handler: ${error.message}`);
    logger.error(`Error stack: ${error.stack}`);
    await ctx.answerCbQuery('حدث خطأ أثناء الانتقال للسؤال التالي');
    
    // Try to send a more detailed error message to the user
    try {
      await ctx.reply('حدث خطأ أثناء الانتقال للسؤال التالي. يرجى إعادة بدء الاختبار من القائمة الرئيسية.');
    } catch (replyError) {
      logger.error(`Could not send error reply: ${replyError.message}`);
    }
  }
});

// Register handler for detailed report
bot.action('show_detailed_report', async (ctx) => {
  try {
    const userId = ctx.from.id;
    logger.user(`User ${userId} requested detailed quiz report`);
    
    // Get session to check if we're already showing a report
    const session = getSession(userId);
    if (session && session.showingDetailedReport) {
      logger.debug(`Already showing detailed report for user ${userId}, ignoring duplicate request`);
      await ctx.answerCbQuery('جاري عرض التقرير...');
      return;
    }
    
    // Process the report request
    await messageHandlers.showDetailedReport(ctx);
    
    // Update callback
    await ctx.answerCbQuery('عرض التقرير المفصل');
  } catch (error) {
    logger.error(`Error handling show_detailed_report action: ${error.message}`);
    await ctx.reply('حدث خطأ أثناء عرض التقرير المفصل. يرجى المحاولة مرة أخرى.');
    
    // In case of error, ensure we reset the flag
    try {
      updateSession(userId, { showingDetailedReport: false });
    } catch (e) {
      logger.error(`Error resetting showingDetailedReport flag: ${e.message}`);
    }
  }
});

// Add handler for text messages with hash values for admin settings
// This MUST come before the general text handler
bot.on('text', async (ctx, next) => {
  // If message starts with # and the user is an admin, handle it specially
  if (ctx.message.text && ctx.message.text.startsWith('#')) {
    const isUserAdmin = await isAdmin(ctx.from.id);
    if (isUserAdmin) {
      try {
        const handled = await handleHashNumberMessage(ctx);
        if (handled) {
          return; // Message was handled by the admin handler
        }
      } catch (error) {
        logger.error(`Error handling # message: ${error.message}`);
      }
    }
  }
  
  // If not handled as admin command, proceed to next middleware
  return next();
});

// Register text message handler for feedback suggestions
bot.on('text', async (ctx) => {
  const userId = ctx.from.id;
  const session = messageHandlers.getSession(userId);
  
  // Check if the user is awaiting feedback submission
  if (session && session.state === 'awaiting_feedback_suggestion') {
    await messageHandlers.handleSuggestionText(ctx);
    return;
  }
  
  // Handle other text messages (add your existing text handlers here)
  const text = ctx.message.text.trim();
  // If the message looks like a topic request with MCQ/TF keywords, create questions
  if (text.length > 3 && (text.includes('MCQ') || text.includes('TF') || 
      text.includes('اختيار') || text.includes('متعدد') || text.includes('صح') || text.includes('خطأ'))) {
    
    // Create processing message with initial status and modern look
    const processingMsg = await ctx.reply('<b>جاري توليد الأسئلة ⏳ ⚡ ⚡</b>\n\n⬜⬜⬜⬜⬜⬜⬜⬜⬜⬜⬜⬜⬜⬜⬜\n🔍 <b>  0%</b>\nبدء العملية...', { parse_mode: 'HTML' });
    
    // Determine the question type from the text
    let questionType = 'MCQ'; // Default to MCQ
    if (text.toLowerCase().includes('tf') || text.includes('صح') || text.includes('خطأ')) {
      questionType = 'TF';
    } else if (text.toLowerCase().includes('mcq') || text.includes('متعدد') || text.includes('اختيار')) {
      questionType = 'MCQ';
    }
    
    // Create a session if it doesn't exist
    let userSession = getSession(userId);
    if (!userSession) {
      userSession = { type: questionType, count: 15 };
      saveSession(userId, userSession);
    } else {
      userSession.type = questionType;
      userSession.count = 15;
      saveSession(userId, userSession);
    }
    
    // Process the content with proper parameters
    await messageHandlers.processContentAndGenerateQuestions(ctx, text, userSession, processingMsg.message_id);
    return;
  }
  
  // If none of the above conditions are met, show the main menu
  await ctx.reply('اختر نوع السؤال:', {
    reply_markup: {
      inline_keyboard: [
        [
          { text: '📝 اسئلة خيارات متعددة (MCQ)', callback_data: 'MCQ' },
          { text: '✅ أسئلة صح/خطأ (TF)', callback_data: 'TF' }
        ],
        [
          { text: '❓ كيفية عمل البوت', callback_data: 'how_bot_works' }
        ]
      ]
    }
  });
});

// Set up message handlers with error handling
bot.on(message('photo'), async (ctx) => {
  try {
    logger.user(`Received photo from ${ctx.from.id}`);
    await messageHandlers.handlePhotoMessage(ctx);
  } catch (error) {
    logger.error(`Error handling photo: ${error.message}`);
    await ctx.reply('حدث خطأ أثناء معالجة الصورة. يرجى المحاولة مرة أخرى.');
  }
});

bot.on(message('document'), async (ctx) => {
  try {
    logger.user(`Received document from ${ctx.from.id}`);
    await messageHandlers.handleDocumentMessage(ctx);
  } catch (error) {
    logger.error(`Error handling document: ${error.message}`);
    await ctx.reply('حدث خطأ أثناء معالجة المستند. يرجى المحاولة مرة أخرى.');
  }
});

// Error handling
bot.catch((err, ctx) => {
  logger.error(`Error for ${ctx.updateType}: ${err.message}`);
  ctx.reply('حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى لاحقًا.');
});

// Start the bot
try {
  logger.info('Starting bot polling...');
  bot.launch();
  logger.success('Bot started successfully!');

  // Enable graceful stop
  process.once('SIGINT', () => {
    logger.info('Bot stopping...');
    bot.stop('SIGINT');
  });
  process.once('SIGTERM', () => {
    logger.info('Bot stopping...');
    bot.stop('SIGTERM');
  });
} catch (error) {
  logger.error(`Failed to start bot: ${error.message}`);
  process.exit(1);
}

// Register handler for MCQ button
bot.action('mcq', async (ctx) => {
  try {
    logger.user(`User ${ctx.from.id} selected MCQ questions`);
    
    // Set user session state to MCQ
    await saveSession(ctx.from.id, { type: 'MCQ' });
    
    // Reply with instructions
    return ctx.reply('لقد اخترت أسئلة الاختيار من متعدد (MCQ). يرجى إرسال صورة أو مستند (PDF أو ملف نصي) وسأقوم بإنشاء أسئلة MCQ بناءً على المحتوى.');
  } catch (error) {
    logger.error(`Error handling MCQ action: ${error.message}`);
    await ctx.reply('حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.');
  }
});

// Register handler for TF button
bot.action('tf', async (ctx) => {
  try {
    logger.user(`User ${ctx.from.id} selected TF questions`);
    
    // Set user session state to TF
    await saveSession(ctx.from.id, { type: 'TF' });
    
    // Reply with instructions
    return ctx.reply('لقد اخترت أسئلة صح/خطأ (TF). يرجى إرسال صورة أو مستند (PDF أو ملف نصي) وسأقوم بإنشاء أسئلة صح/خطأ بناءً على المحتوى.');
  } catch (error) {
    logger.error(`Error handling TF action: ${error.message}`);
    await ctx.reply('حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.');
  }
});

// Handle MCQ command
bot.command('mcq', async (ctx) => {
  const text = ctx.message.text.trim().split(' ').slice(1).join(' ');
  
  if (!text) {
    await ctx.reply(
      'يرجى تحديد موضوع للأسئلة. مثال:\n' +
      '/mcq الطب النبوي',
      { reply_to_message_id: ctx.message.message_id }
    );
    return;
  }
  
  // Use the optimized non-blocking handler
  messageHandlers.getFromGPT(ctx, text, 'MCQ', 10);
});

// Handle TF command
bot.command('tf', async (ctx) => {
  const text = ctx.message.text.trim().split(' ').slice(1).join(' ');
  
  if (!text) {
    await ctx.reply(
      'يرجى تحديد موضوع للأسئلة. مثال:\n' +
      '/tf الطب النبوي',
      { reply_to_message_id: ctx.message.message_id }
    );
    return;
  }
  
  // Use the optimized non-blocking handler
  messageHandlers.getFromGPT(ctx, text, 'TF', 10);
});

// Register callback query handlers
bot.action(/next_question_(\d+)$/, async (ctx) => await handleNextQuestion(ctx));
bot.action('show_answers', async (ctx) => await showAnswers(ctx));
bot.action('detailed_report', async (ctx) => await showDetailedReport(ctx));
bot.action(/^answer_(\d+)_(.+)$/, async (ctx) => {
  const match = ctx.callbackQuery.data.match(/^answer_(\d+)_(.+)$/);
  await handleUserAnswer(ctx, parseInt(match[1]), match[2]);
});

// Register feedback handlers
bot.action('request_feedback', async (ctx) => await requestFeedback(ctx));
bot.action('feedback_good_direct', async (ctx) => {
  try {
    const userId = ctx.from.id;
    const session = getSession(userId);
    
    if (!session) {
      await ctx.answerCbQuery('حدث خطأ في الجلسة. يرجى المحاولة مرة أخرى.');
      return;
    }
    
    // Prepare feedback data
    const feedbackData = {
      userId: userId,
      username: ctx.from.username || ctx.from.first_name || 'Unknown',
      rating: 'good',
      suggestion: null,
      quizType: session.type || 'Unknown',
      score: session.quizScore ? 
        Math.round((session.quizScore.correct / session.quizScore.total) * 100) : 0
    };
    
    // Get the feedback service
    const feedbackService = require('./services/feedbackService');
    
    logger.info(`Processing good feedback from user ${userId}`);
    
    // Try to save to database first
    let saved = false;
    try {
      saved = await feedbackService.saveFeedback(feedbackData);
      if (saved) {
        logger.success(`Successfully saved feedback to database for user ${userId}`);
      } else {
        logger.warn(`Database feedback save returned false for user ${userId}, trying direct JSON save`);
        saved = await feedbackService.saveFeedbackDirectToJson(feedbackData);
        if (saved) {
          logger.success(`Successfully saved feedback directly to JSON for user ${userId}`);
        }
      }
    } catch (error) {
      logger.error(`Error saving feedback: ${error.message}`);
      logger.error(`Error stack: ${error.stack}`);
      
      // Try direct JSON save as fallback
      try {
        saved = await feedbackService.saveFeedbackDirectToJson(feedbackData);
        if (saved) {
          logger.success(`Successfully saved feedback to JSON after database error for user ${userId}`);
        } else {
          logger.error(`Failed to save feedback to JSON after database error for user ${userId}`);
        }
      } catch (jsonError) {
        logger.error(`Also failed to save feedback to JSON: ${jsonError.message}`);
      }
    }
    
    // Delete the feedback message
    if (session.feedbackMessageId) {
      try {
        await ctx.telegram.deleteMessage(ctx.chat.id, session.feedbackMessageId);
      } catch (error) {
        logger.debug(`Could not delete feedback message: ${error.message}`);
      }
    }
    
    // Reset session state
    await updateSession(userId, { 
      state: null,
      feedbackMessageId: null
    });
    
    // Thank the user
    await ctx.reply('شكراً على تقييمك الإيجابي! 🙏');
    await ctx.answerCbQuery(saved ? 'تم إرسال تقييمك بنجاح' : 'تم استلام تقييمك');
  } catch (error) {
    logger.error(`Error handling direct good feedback: ${error.message}`);
    await ctx.answerCbQuery('حدث خطأ أثناء حفظ تقييمك');
  }
});

bot.action('feedback_bad_direct', async (ctx) => {
  try {
    const userId = ctx.from.id;
    const session = getSession(userId);
    
    if (!session) {
      await ctx.answerCbQuery('حدث خطأ في الجلسة. يرجى المحاولة مرة أخرى.');
      return;
    }
    
    // Prepare feedback data
    const feedbackData = {
      userId: userId,
      username: ctx.from.username || ctx.from.first_name || 'Unknown',
      rating: 'bad',
      suggestion: null,
      quizType: session.type || 'Unknown',
      score: session.quizScore ? 
        Math.round((session.quizScore.correct / session.quizScore.total) * 100) : 0
    };
    
    // Get the feedback service
    const feedbackService = require('./services/feedbackService');
    
    logger.info(`Processing bad feedback from user ${userId}`);
    
    // Try to save to database first
    let saved = false;
    try {
      saved = await feedbackService.saveFeedback(feedbackData);
      if (saved) {
        logger.success(`Successfully saved feedback to database for user ${userId}`);
      } else {
        logger.warn(`Database feedback save returned false for user ${userId}, trying direct JSON save`);
        saved = await feedbackService.saveFeedbackDirectToJson(feedbackData);
        if (saved) {
          logger.success(`Successfully saved feedback directly to JSON for user ${userId}`);
        }
      }
    } catch (error) {
      logger.error(`Error saving feedback: ${error.message}`);
      logger.error(`Error stack: ${error.stack}`);
      
      // Try direct JSON save as fallback
      try {
        saved = await feedbackService.saveFeedbackDirectToJson(feedbackData);
        if (saved) {
          logger.success(`Successfully saved feedback to JSON after database error for user ${userId}`);
        } else {
          logger.error(`Failed to save feedback to JSON after database error for user ${userId}`);
        }
      } catch (jsonError) {
        logger.error(`Also failed to save feedback to JSON: ${jsonError.message}`);
      }
    }
    
    // Delete the feedback message
    if (session.feedbackMessageId) {
      try {
        await ctx.telegram.deleteMessage(ctx.chat.id, session.feedbackMessageId);
      } catch (error) {
        logger.debug(`Could not delete feedback message: ${error.message}`);
      }
    }
    
    // Reset session state
    await updateSession(userId, { 
      state: null,
      feedbackMessageId: null
    });
    
    // Thank the user
    await ctx.reply('شكراً على ملاحظاتك. سنعمل على تحسين البوت! 🙏');
    await ctx.answerCbQuery(saved ? 'تم إرسال تقييمك بنجاح' : 'تم استلام تقييمك');
  } catch (error) {
    logger.error(`Error handling direct bad feedback: ${error.message}`);
    await ctx.answerCbQuery('حدث خطأ أثناء حفظ تقييمك');
  }
});

bot.action('feedback_opinion', async (ctx) => await handleFeedbackRating(ctx, 'opinion'));
bot.action('feedback_good', async (ctx) => await handleFeedbackRating(ctx, 'good'));
bot.action('feedback_bad', async (ctx) => await handleFeedbackRating(ctx, 'bad'));
bot.action('no_suggestion', async (ctx) => await handleNoSuggestion(ctx));
bot.action('cancel_feedback', async (ctx) => await cancelSuggestion(ctx));

// Register feedback admin action handlers
bot.action('refresh_feedback', async (ctx) => {
  try {
    // Delete the previous message to keep the chat clean
    if (ctx.callbackQuery && ctx.callbackQuery.message) {
      await ctx.telegram.deleteMessage(
        ctx.callbackQuery.message.chat.id,
        ctx.callbackQuery.message.message_id
      ).catch(err => {
        // Only log non-"message to delete not found" errors
        if (!err.message.includes("message to delete not found")) {
          logger.debug(`Could not delete previous message: ${err.message}`);
        }
      });
    }
    
    await ctx.answerCbQuery('جاري تحديث البيانات...');
    await handleFeedback(ctx, 0); // Always start from the first page when refreshing
  } catch (error) {
    logger.error('Error refreshing feedback:');
    await ctx.answerCbQuery('حدث خطأ أثناء تحديث البيانات');
  }
});

bot.action('mark_all_read', async (ctx) => {
  try {
    // Delete the previous message to keep the chat clean
    if (ctx.callbackQuery && ctx.callbackQuery.message) {
      await ctx.telegram.deleteMessage(
        ctx.callbackQuery.message.chat.id,
        ctx.callbackQuery.message.message_id
      ).catch(err => {
        // Only log non-"message to delete not found" errors
        if (!err.message.includes("message to delete not found")) {
          logger.debug(`Could not delete previous message: ${err.message}`);
        }
      });
    }
    
    const { markAllFeedbackAsRead } = require('./services/feedbackService');
    await markAllFeedbackAsRead();
    await ctx.answerCbQuery('تم تعليم جميع الملاحظات كمقروءة');
    await handleFeedback(ctx, 0); // Always start from the first page
  } catch (error) {
    logger.error('Error marking all feedback as read:');
    await ctx.answerCbQuery('حدث خطأ أثناء تحديث حالة الملاحظات');
  }
});

// Add pagination handlers
bot.action(/feedback_page_(\d+)/, async (ctx) => {
  try {
    // Delete the previous message to keep the chat clean
    if (ctx.callbackQuery && ctx.callbackQuery.message) {
      await ctx.telegram.deleteMessage(
        ctx.callbackQuery.message.chat.id,
        ctx.callbackQuery.message.message_id
      ).catch(err => {
        // Only log non-"message to delete not found" errors
        if (!err.message.includes("message to delete not found")) {
          logger.debug(`Could not delete previous message: ${err.message}`);
        }
      });
    }
    
    const page = parseInt(ctx.match[1], 10);
    await ctx.answerCbQuery(`عرض الصفحة ${page + 1}`);
    await handleFeedback(ctx, page);
  } catch (error) {
    logger.error('Error navigating feedback pages:');
    await ctx.answerCbQuery('حدث خطأ أثناء تصفح الملاحظات');
  }
});

// Add model status to admin menu
bot.action('admin_model_status', async (ctx) => await handleModelStatus(ctx));
bot.action('admin_test_models', async (ctx) => await handleTestModels(ctx));

// Admin settings callbacks
bot.action('admin_setting_questions', async (ctx) => await handleQuestionsPerPageSetting(ctx));
bot.action('admin_setting_image_questions', async (ctx) => await handleImageQuestionsCountSetting(ctx));
bot.action('admin_setting_files', async (ctx) => await handleFilesPerDaySetting(ctx));
bot.action('admin_save_env', async (ctx) => await saveSettingsToEnv(ctx));
bot.action('admin_back_to_settings', async (ctx) => await handleAdminSettings(ctx));
bot.action('admin_set_questions_custom', async (ctx) => await handleCustomQuestionsInput(ctx));
bot.action('admin_set_files_custom', async (ctx) => await handleCustomFilesInput(ctx));

// Question per page settings
bot.action('admin_set_questions_1', async (ctx) => await setQuestionsPerPage(ctx, 1));
bot.action('admin_set_questions_2', async (ctx) => await setQuestionsPerPage(ctx, 2));
bot.action('admin_set_questions_3', async (ctx) => await setQuestionsPerPage(ctx, 3));
bot.action('admin_set_questions_4', async (ctx) => await setQuestionsPerPage(ctx, 4));
bot.action('admin_set_questions_5', async (ctx) => await setQuestionsPerPage(ctx, 5));
bot.action('admin_set_questions_10', async (ctx) => await setQuestionsPerPage(ctx, 10));

// Image questions count settings
bot.action('admin_set_image_questions_3', async (ctx) => await setImageQuestionsCount(ctx, 3));
bot.action('admin_set_image_questions_5', async (ctx) => await setImageQuestionsCount(ctx, 5));
bot.action('admin_set_image_questions_7', async (ctx) => await setImageQuestionsCount(ctx, 7));
bot.action('admin_set_image_questions_10', async (ctx) => await setImageQuestionsCount(ctx, 10));
bot.action('admin_set_image_questions_15', async (ctx) => await setImageQuestionsCount(ctx, 15));
bot.action('admin_set_image_questions_20', async (ctx) => await setImageQuestionsCount(ctx, 20));

// Files per day settings
bot.action('admin_set_files_1', async (ctx) => await setFilesPerDay(ctx, 1));
bot.action('admin_set_files_3', async (ctx) => await setFilesPerDay(ctx, 3));
bot.action('admin_set_files_5', async (ctx) => await setFilesPerDay(ctx, 5));
bot.action('admin_set_files_10', async (ctx) => await setFilesPerDay(ctx, 10));
bot.action('admin_set_files_15', async (ctx) => await setFilesPerDay(ctx, 15));
bot.action('admin_set_files_20', async (ctx) => await setFilesPerDay(ctx, 20));

// Dynamic custom handlers
bot.action(/admin_set_questions_(\d+)$/, async (ctx) => {
  try {
    const value = parseInt(ctx.match[1], 10);
    if (!isNaN(value) && value > 0) {
      await setQuestionsPerPage(ctx, value);
    } else {
      await ctx.answerCbQuery('Invalid value');
    }
  } catch (error) {
    logger.error(`Error handling dynamic question value: ${error.message}`);
    await ctx.answerCbQuery('An error occurred');
  }
});

bot.action(/admin_set_image_questions_(\d+)$/, async (ctx) => {
  try {
    const value = parseInt(ctx.match[1], 10);
    if (!isNaN(value) && value > 0) {
      await setImageQuestionsCount(ctx, value);
    } else {
      await ctx.answerCbQuery('Invalid value');
    }
  } catch (error) {
    logger.error(`Error handling dynamic image question value: ${error.message}`);
    await ctx.answerCbQuery('An error occurred');
  }
});

bot.action(/admin_set_files_(\d+)$/, async (ctx) => {
  try {
    const value = parseInt(ctx.match[1], 10);
    if (!isNaN(value) && value > 0) {
      await setFilesPerDay(ctx, value);
    } else {
      await ctx.answerCbQuery('Invalid value');
    }
  } catch (error) {
    logger.error(`Error handling dynamic files value: ${error.message}`);
    await ctx.answerCbQuery('An error occurred');
  }
});

// Add feedback JSON viewer handlers
bot.action('admin_view_feedback_json', async (ctx) => {
  try {
    await handleFeedbackJsonViewer(ctx, 0); // Start at page 0
  } catch (error) {
    logger.error(`Error handling admin_view_feedback_json: ${error.message}`);
    await ctx.answerCbQuery('An error occurred while loading feedback data');
  }
});

// Handle feedback JSON viewer pagination
bot.action(/feedback_json_page_(\d+)/, async (ctx) => {
  try {
    const page = parseInt(ctx.match[1], 10);
    await handleFeedbackJsonViewer(ctx, page);
  } catch (error) {
    logger.error(`Error handling feedback_json_page: ${error.message}`);
    await ctx.answerCbQuery('An error occurred while loading feedback page');
  }
});

// Handle marking all feedback as read
bot.action('feedback_json_mark_all_read', async (ctx) => {
  try {
    const feedbackService = require('./services/feedbackService');
    await feedbackService.markAllFeedbackAsRead();
    
    // Refresh the current page
    const pageMatch = ctx.callbackQuery.message.text.match(/Page (\d+)\//);
    let currentPage = 0;
    if (pageMatch && pageMatch[1]) {
      currentPage = parseInt(pageMatch[1], 10) - 1; // Convert from 1-based to 0-based
    }
    
    await handleFeedbackJsonViewer(ctx, currentPage);
    await ctx.answerCbQuery('All feedback marked as read');
  } catch (error) {
    logger.error(`Error handling feedback_json_mark_all_read: ${error.message}`);
    await ctx.answerCbQuery('An error occurred while marking feedback as read');
  }
});

// Handle no-action buttons (disabled pagination)
bot.action('feedback_json_no_action', async (ctx) => {
  await ctx.answerCbQuery('No more pages in this direction');
});

bot.action('feedback_json_current_page', async (ctx) => {
  await ctx.answerCbQuery(`Current page`);
});

// Handle user JSON viewer
bot.action('admin_view_users_json', async (ctx) => {
  try {
    await handleUserJsonViewer(ctx, 0); // Start at page 0
  } catch (error) {
    logger.error(`Error handling admin_view_users_json: ${error.message}`);
    await ctx.answerCbQuery('An error occurred while loading user data');
  }
});

// Handle system status view
bot.action('admin_view_system_status', async (ctx) => {
  try {
    await handleSystemStatus(ctx);
  } catch (error) {
    logger.error(`Error handling admin_view_system_status: ${error.message}`);
    await ctx.answerCbQuery('An error occurred while loading system status');
  }
});

// Handle "How Bot Works" action
bot.action('how_bot_works', async (ctx) => {
  try {
    logger.user(`User ${ctx.from.id} requested how bot works info`);
    
    const howItWorksText = `
<b>✨ كيفية عمل البوت ✨</b>

<b>🤖 البوت يعمل بطريقة سهلة وسريعة:</b>

🔹 <b>الخطوة 1:</b> اختر نوع الأسئلة
   • خيارات متعددة (MCQ) 📝
   • صح/خطأ (TF) ✅

🔹 <b>الخطوة 2:</b> أرسل المحتوى التعليمي
   • صورة تحتوي على نص 🖼️
   • ملف PDF 📑

🔹 <b>الخطوة 3:</b> البوت يقوم بتحليل المحتوى
   • يستخرج المعلومات المهمة 🔍
   • ينشئ أسئلة ذكية 🧠

🔹 <b>الخطوة 4:</b> اختر ما تريد
   • اختبار تفاعلي مباشر ⚡
   • عرض الأسئلة مع الإجابات 📋

<b>🚀 مطور البوت</b> @sytus
`;

    // Edit the current message instead of sending a new one
    await ctx.editMessageText(howItWorksText, {
      parse_mode: 'HTML',
      reply_markup: {
        inline_keyboard: [
          [
            { text: '🏠 العودة للقائمة الرئيسية', callback_data: 'back_to_menu' }
          ]
        ]
      }
    });
    
    // Answer the callback query to remove the loading indicator
    await ctx.answerCbQuery('كيفية عمل البوت ✨');
    
  } catch (error) {
    logger.error(`Error handling how_bot_works action: ${error.message}`);
    await ctx.answerCbQuery('حدث خطأ أثناء معالجة طلبك');
  }
});

// Add handler for returning to main menu from how_bot_works
bot.action('back_to_menu', async (ctx) => {
  try {
    logger.user(`User ${ctx.from.id} returned to main menu from how_bot_works`);
    
    // Edit the message back to the main menu
    await ctx.editMessageText('اختر نوع السؤال:', {
      reply_markup: {
        inline_keyboard: [
          [
            { text: '📝 اسئلة خيارات متعددة (MCQ)', callback_data: 'MCQ' },
            { text: '✅ أسئلة صح/خطأ (TF)', callback_data: 'TF' }
          ],
          [
            { text: '❓ كيفية عمل البوت', callback_data: 'how_bot_works' }
          ]
        ]
      }
    });
    
    // Answer the callback query
    await ctx.answerCbQuery('القائمة الرئيسية');
    
  } catch (error) {
    logger.error(`Error returning to main menu: ${error.message}`);
    await ctx.answerCbQuery('حدث خطأ أثناء العودة للقائمة الرئيسية');
  }
});

// Handle user JSON viewer pagination
bot.action(/users_json_page_(\d+)/, async (ctx) => {
  try {
    const page = parseInt(ctx.match[1], 10);
    await handleUserJsonViewer(ctx, page);
  } catch (error) {
    logger.error(`Error handling users_json_page: ${error.message}`);
    await ctx.answerCbQuery('An error occurred while loading user page');
  }
});

// Handle no-action buttons for user pagination
bot.action('users_json_no_action', async (ctx) => {
  await ctx.answerCbQuery('No more pages in this direction');
});

bot.action('users_json_current_page', async (ctx) => {
  await ctx.answerCbQuery(`Current page`);
});

// Handle user JSON download
bot.action('users_json_download', async (ctx) => {
  try {
    await handleUserJson(ctx);
    await ctx.answerCbQuery('Downloading user data');
  } catch (error) {
    logger.error(`Error handling users_json_download: ${error.message}`);
    await ctx.answerCbQuery('An error occurred while downloading user data');
  }
});

// Direct user JSON download command
bot.command('usersjson', async (ctx) => {
  try {
    // Check if user is admin
    const isUserAdmin = await isAdmin(ctx.from.id);
    if (!isUserAdmin) {
      return ctx.reply('You do not have permission to use this command.');
    }
    
    // Only call the handler if user is an admin
    await handleUserJson(ctx);
  } catch (error) {
    logger.error(`Error in /usersjson command: ${error.message}`);
    ctx.reply('An error occurred while processing your request.');
  }
});

// Add this feedback_json_download handler after the other feedback_json actions
bot.action('feedback_json_download', async (ctx) => {
  try {
    await handleFeedbackJson(ctx);
    await ctx.answerCbQuery('Downloading feedback data');
  } catch (error) {
    logger.error(`Error handling feedback_json_download: ${error.message}`);
    await ctx.answerCbQuery('An error occurred while downloading feedback data');
  }
});

// Setup graceful shutdown handlers
let isShuttingDown = false;

// Function to clean up temp files
const cleanupTempFiles = () => {
  try {
    const tempDir = path.join(__dirname, '../temp');
    if (fs.existsSync(tempDir)) {
      const files = fs.readdirSync(tempDir);
      let count = 0;
      
      files.forEach(file => {
        const filePath = path.join(tempDir, file);
        try {
          // Skip directories and only remove files
          if (fs.statSync(filePath).isFile()) {
            fs.unlinkSync(filePath);
            count++;
          }
        } catch (err) {
          logger.error(`Error removing temp file ${filePath}: ${err.message}`);
        }
      });
      
      logger.info(`Cleaned up ${count} temporary files`);
    }
  } catch (err) {
    logger.error(`Error cleaning up temp files: ${err.message}`);
  }
};

// Function to perform a graceful shutdown
const performShutdown = async (signal) => {
  if (isShuttingDown) return;
  isShuttingDown = true;
  
  logger.info(`Received ${signal}, shutting down gracefully...`);
  
  try {
    // Close Telegram bot
    logger.info('Stopping Telegram bot...');
    await bot.stop();
    logger.info('Telegram bot stopped');
    
    // Close all database connections
    logger.info('Closing database connections...');
    if (database && typeof database.getDb === 'function') {
      const db = database.getDb();
      if (db) {
        await new Promise((resolve, reject) => {
          db.close((err) => {
            if (err) {
              logger.error(`Error closing database: ${err.message}`);
              reject(err);
            } else {
              logger.info('Database closed successfully');
              resolve();
            }
          });
        });
      }
    }
    
    // Clean up temp files
    logger.info('Cleaning up temporary files...');
    cleanupTempFiles();
    
    // Final log
    logger.info('Shutdown complete. Goodbye!');
    
    // Exit after short delay to allow final logs to be written
    setTimeout(() => {
      process.exit(0);
    }, 500);
  } catch (err) {
    logger.error(`Error during graceful shutdown: ${err.message}`);
    // Force exit after error
    process.exit(1);
  }
};

// Register shutdown handlers
process.on('SIGINT', () => performShutdown('SIGINT'));
process.on('SIGTERM', () => performShutdown('SIGTERM'));
process.on('SIGHUP', () => performShutdown('SIGHUP'));

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error(`Uncaught exception: ${err.message}`);
  logger.error(err.stack);
  performShutdown('uncaughtException');
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled promise rejection:', reason);
  // Don't shut down for unhandled rejections, just log them
}); 