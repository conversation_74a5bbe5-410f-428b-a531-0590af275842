// Bot command handlers
const { Markup } = require('telegraf');
const { isAdmin, storeUser, logUser, getTotalUsers } = require('../utils/userUtils');
const { getCurrentKeyIndex } = require('../services/apiService');
const { getCacheHitRate, db } = require('../database/database');
const { getSession, saveSession } = require('../services/sessionService');
const { sendMenu, sendAdminPanel, sendKeysPanel } = require('./menuHandlers');

/**
 * Handle /start command
 * @param {Object} ctx Telegram context
 */
async function handleStart(ctx) {
  const userId = ctx.from.id;
  
  try {
    // Store user in database
    await storeUser(ctx.from);
    
    // Log user starting the bot
    await logUser(userId, 'start');
    
    // Send welcome message
    await ctx.reply(`Welcome to the MCQ & True/False Generator Bot! 🎓\n\nI can help you create multiple-choice and true/false questions from your content. Just send me your study material, and I'll turn it into quiz questions!`);
    
    // Send main menu
    await ctx.reply('Choose a question type:', {
      reply_markup: {
        inline_keyboard: [
          [
            { text: '📝 Multiple Choice Questions', callback_data: 'MCQ' },
            { text: '✅ True/False Questions', callback_data: 'TF' }
          ]
        ]
      }
    });
  } catch (error) {
    console.error('Error in handleStart:', error);
    ctx.reply('An error occurred. Please try again later.');
  }
}

/**
 * Handle /help command
 * @param {Object} ctx Telegram context
 */
async function handleHelp(ctx) {
  const userId = ctx.from.id;
  
  try {
    // Log user requesting help
    await logUser(userId, 'help');
    
    const helpMessage = `
*MCQ & T/F Generator Bot Help* 📚

*Available Commands:*
/start - Start the bot and show the main menu
/help - Show this help message
/stats - Show usage statistics (admin only)
/admin - Access admin panel (admin only)
/keys - Manage API keys (admin only)

*How to use:*
1. Choose a question type (MCQ or T/F)
2. Send your study material as text or image
3. The bot will generate questions based on your content
4. Review the questions and answers

*Supported file types:*
- Direct text messages
- Images with text (OCR will be used)

*Need more help?*
If you have any issues or suggestions, please use the feedback buttons under your quiz results.
`;

    await ctx.replyWithMarkdown(helpMessage);
  } catch (error) {
    console.error('Error in handleHelp:', error);
    ctx.reply('An error occurred. Please try again later.');
  }
}

/**
 * Handle /stats command (admin only)
 * @param {Object} ctx Telegram context
 */
async function handleStats(ctx) {
  const userId = ctx.from.id;
  
  try {
    // Check if user is admin
    if (!await isAdmin(userId)) {
      return ctx.reply('This command is only available to administrators.');
    }
    
    // Log admin accessing stats
    await logUser(userId, 'stats');
    
    await ctx.reply('Stats command received. This feature is coming soon.');
  } catch (error) {
    console.error('Error in handleStats:', error);
    ctx.reply('An error occurred. Please try again later.');
  }
}

/**
 * Handle /admin command (admin only)
 * @param {Object} ctx Telegram context
 */
async function handleAdmin(ctx) {
  const userId = ctx.from.id;
  
  try {
    // Check if user is admin
    if (!await isAdmin(userId)) {
      return ctx.reply('This command is only available to administrators.');
    }
    
    // Log admin accessing admin panel
    await logUser(userId, 'admin');
    
    await ctx.reply('Admin panel is currently under development.');
  } catch (error) {
    console.error('Error in handleAdmin:', error);
    ctx.reply('An error occurred. Please try again later.');
  }
}

/**
 * Handle /keys command (admin only)
 * @param {Object} ctx Telegram context
 */
async function handleKeys(ctx) {
  const userId = ctx.from.id;
  
  try {
    // Check if user is admin
    if (!await isAdmin(userId)) {
      return ctx.reply('This command is only available to administrators.');
    }
    
    // Log admin accessing keys management
    await logUser(userId, 'keys');
    
    await ctx.reply('API key management is currently under development.');
  } catch (error) {
    console.error('Error in handleKeys:', error);
    ctx.reply('An error occurred. Please try again later.');
  }
}

/**
 * Handle /users command to show user statistics
 * @param {Object} ctx Telegram context
 */
async function handleUsers(ctx) {
  const userId = ctx.from.id;
  
  try {
    // Check if user is admin
    const admin = await isAdmin(userId);
    if (!admin) {
      await ctx.reply('Sorry, this command is only available to admins.');
      return;
    }
    
    // Get all users
    const users = await require('../utils/userUtils').getAllUsers();
    const totalUsers = await getTotalUsers();
    
    // Calculate statistics
    const now = new Date();
    const oneWeekAgo = new Date(now);
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
    const oneDayAgo = new Date(now);
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);
    
    // Filter users active in the last week and day by comparing ISO dates
    const activeUsers = users.filter(u => {
      const lastActivity = new Date(u.last_activity);
      return lastActivity >= oneWeekAgo;
    }).length;
    
    const todayUsers = users.filter(u => {
      const lastActivity = new Date(u.last_activity);
      return lastActivity >= oneDayAgo;
    }).length;
    
    const botUsers = users.filter(u => u.is_bot).length;
    const usersWithUsername = users.filter(u => u.username).length;
    
    // Prepare message
    let message = `📊 *User Statistics*\n\n`;
    message += `👥 Total Users: ${totalUsers}\n`;
    message += `👤 Active Users (7 days): ${activeUsers}\n`;
    message += `👤 Active Users (24h): ${todayUsers}\n`;
    message += `🤖 Bot Users: ${botUsers}\n`;
    message += `🔖 Users with Username: ${usersWithUsername}\n\n`;
    
    // List newest 5 users sorted by join date (ISO string)
    const newestUsers = [...users].sort((a, b) => {
      return new Date(b.joined_at) - new Date(a.joined_at);
    }).slice(0, 5);
    
    message += `*Newest Users:*\n`;
    newestUsers.forEach((user, i) => {
      const joinDate = new Date(user.joined_at);
      const formattedDate = joinDate.toLocaleDateString();
      message += `${i+1}. ${user.full_name || user.username || user.id} (${formattedDate})\n`;
    });
    
    // Send message
    await ctx.reply(message, {
      parse_mode: 'Markdown'
    });
    
  } catch (error) {
    console.error('Error in handleUsers:', error);
    await ctx.reply('An error occurred while fetching user statistics.');
  }
}

/**
 * Handle /feedback command
 * @param {object} ctx - Telegram context
 * @param {number} page - Page number (starts from 0)
 * @returns {Promise<void>}
 */
async function handleFeedback(ctx, page = 0) {
  try {
    const { isAdmin } = require('../utils/userUtils');
    
    // Check if user is admin
    if (!await isAdmin(ctx.from.id)) {
      await ctx.reply('عذراً، هذا الأمر متاح للمشرفين فقط. 🔒');
      return;
    }
    
    // Extract page from context if available (used by callback handlers)
    if (typeof page !== 'number' && ctx.callbackQuery && ctx.callbackQuery.data) {
      const match = ctx.callbackQuery.data.match(/feedback_page_(\d+)/);
      if (match) {
        page = parseInt(match[1], 10);
      }
    }
    
    // Delete all previous messages with the command
    if (global.adminFeedbackMsgIds && global.adminFeedbackMsgIds[ctx.chat.id]) {
      for (const msgId of global.adminFeedbackMsgIds[ctx.chat.id]) {
        try {
          await ctx.telegram.deleteMessage(ctx.chat.id, msgId)
            .catch(error => {
              // Only log non-"message to delete not found" errors
              if (!error.message.includes("message to delete not found")) {
                console.log(`Could not delete admin feedback message: ${error.message}`);
              }
            });
        } catch (error) {
          // Silent catch - we don't want to interrupt the flow if a message can't be deleted
          if (!error.message.includes("message to delete not found")) {
            console.log(`Error deleting message: ${error.message}`);
          }
        }
      }
    }
    
    // Initialize or reset the message IDs array for this chat
    if (!global.adminFeedbackMsgIds) global.adminFeedbackMsgIds = {};
    global.adminFeedbackMsgIds[ctx.chat.id] = [];
    
    // Get feedback data
    const { getAllFeedback, getFeedbackStats } = require('../services/feedbackService');
    const stats = await getFeedbackStats();
    
    // Pagination settings
    const ITEMS_PER_PAGE = 5;
    const allFeedback = await getAllFeedback(false); // Get all feedback for pagination
    const totalPages = Math.ceil(allFeedback.length / ITEMS_PER_PAGE);
    
    // Ensure page is within valid range
    page = Math.max(0, Math.min(page, totalPages - 1));
    
    // Get paginated feedback
    const startIndex = page * ITEMS_PER_PAGE;
    const feedback = allFeedback.slice(startIndex, startIndex + ITEMS_PER_PAGE);
    
    // Format stats message
    let statsMessage = `<b>📊 إحصائيات الملاحظات:</b>\n\n`;
    statsMessage += `<b>إجمالي الملاحظات:</b> ${stats.total}\n`;
    statsMessage += `<b>ملاحظات غير مقروءة:</b> ${stats.unread}\n\n`;
    statsMessage += `<b>التقييمات:</b>\n`;
    statsMessage += `👍 جيد: ${stats.ratings.good || 0}\n`;
    statsMessage += `👎 سيء: ${stats.ratings.bad || 0}\n`;
    statsMessage += `✍️ رأي: ${stats.ratings.opinion || 0}\n`;
    
    // Send stats message and track its ID
    const statsMsg = await ctx.reply(statsMessage, { parse_mode: 'HTML' });
    global.adminFeedbackMsgIds[ctx.chat.id].push(statsMsg.message_id);
    
    // If no feedback, return
    if (feedback.length === 0) {
      const noFeedbackMsg = await ctx.reply('لا توجد ملاحظات حتى الآن. 📭');
      global.adminFeedbackMsgIds[ctx.chat.id].push(noFeedbackMsg.message_id);
      return;
    }
    
    // Generate feedback message
    let feedbackMessage = `<b>💬 الملاحظات (صفحة ${page + 1} من ${totalPages}):</b>\n\n`;
    let counter = startIndex + 1;
    
    for (const entry of feedback) {
      const dateStr = new Date(entry.timestamp).toLocaleString();
      const username = entry.username || 'مستخدم مجهول';
      let ratingEmoji = '❓';
      
      if (entry.rating === 'good') ratingEmoji = '👍';
      else if (entry.rating === 'bad') ratingEmoji = '👎';
      else if (entry.rating === 'opinion') ratingEmoji = '✍️';
      
      feedbackMessage += `<b>${counter}. من:</b> ${username}\n` +
                        `<b>التاريخ:</b> ${dateStr}\n` +
                        `<b>التقييم:</b> ${ratingEmoji}\n` +
                        `<b>الدرجة:</b> ${entry.score}%\n` +
                        `<b>نوع الاختبار:</b> ${entry.quiz_type || 'غير محدد'}\n` +
                        `<b>الاقتراح:</b> ${entry.suggestion || 'لا يوجد'}\n` +
                        `<b>الحالة:</b> ${entry.is_read ? '✅ مقروء' : '❌ غير مقروء'}\n\n`;
      
      counter++;
    }
    
    // Create pagination buttons
    const paginationButtons = [];
    
    // Previous page button
    if (page > 0) {
      paginationButtons.push({ text: '◀️ السابق', callback_data: `feedback_page_${page - 1}` });
    }
    
    // Next page button
    if (page < totalPages - 1) {
      paginationButtons.push({ text: 'التالي ▶️', callback_data: `feedback_page_${page + 1}` });
    }
    
    // Action buttons row
    const actionButtons = [
      { text: 'تحديث ✨', callback_data: 'refresh_feedback' },
      { text: 'تعليم الكل كمقروء ✅', callback_data: 'mark_all_read' }
    ];
    
    // Create inline keyboard with pagination and action buttons
    const inlineKeyboard = [];
    if (paginationButtons.length > 0) {
      inlineKeyboard.push(paginationButtons);
    }
    inlineKeyboard.push(actionButtons);
    
    // Send feedback message with buttons
    const feedbackMsg = await ctx.reply(feedbackMessage, {
      parse_mode: 'HTML',
      reply_markup: {
        inline_keyboard: inlineKeyboard
      }
    });
    
    global.adminFeedbackMsgIds[ctx.chat.id].push(feedbackMsg.message_id);
    
  } catch (error) {
    console.error('Error in handleFeedback:', error);
    await ctx.reply('حدث خطأ أثناء استرجاع الملاحظات. يرجى المحاولة مرة أخرى لاحقاً.');
  }
}

// Export command handlers
module.exports = {
  handleStart,
  handleHelp,
  handleStats,
  handleAdmin,
  handleKeys,
  handleUsers,
  handleFeedback
}; 