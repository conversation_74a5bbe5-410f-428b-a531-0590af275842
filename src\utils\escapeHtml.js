/**
 * Utility for escaping HTML special characters to prevent XSS attacks
 * in HTML messages sent via Telegram
 */

/**
 * Escape HTML special characters in a string
 * @param {string} text Text to escape
 * @returns {string} Escaped text safe for HTML output
 */
function escapeHtml(text) {
  if (!text || typeof text !== 'string') {
    return '';
  }
  
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;');
}

module.exports = escapeHtml; 