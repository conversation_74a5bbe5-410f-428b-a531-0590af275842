const { exec, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');
const crypto = require('crypto');

// Configuration (can be overridden by environment variables)
const CONFIG = {
    // Python configuration
    pythonPath: process.env.PYTHON_PATH || 'python', // Default to system Python
    extractionScriptName: process.env.EXTRACTION_SCRIPT || 'simple_extraction_service_fixed.py',
    
    // Performance settings
    maxConcurrentProcesses: parseInt(process.env.EXTRACTION_PARALLEL_WORKERS || '6', 10),
    extractionTimeout: parseInt(process.env.EXTRACTION_TIMEOUT_MS || '1200000', 10), // 20 minutes
    
    // Caching settings
    maxCacheSize: parseInt(process.env.EXTRACTION_CACHE_SIZE || '200', 10),
    cacheTTL: parseInt(process.env.EXTRACTION_CACHE_TTL || '14400000', 10), // 4 hours
    
    // File paths
    tempDir: process.env.TEMP_DIR || path.join(__dirname, 'temp'),
    logsDir: process.env.LOGS_DIR || path.join(__dirname, 'logs'),
    
    // Image settings
    largeImageSizeThresholdMB: parseInt(process.env.LARGE_IMAGE_THRESHOLD_MB || '100', 10)
};

// Ensure required directories exist
[CONFIG.tempDir, CONFIG.logsDir].forEach(dir => {
    if (!fs.existsSync(dir)) {
        try {
            fs.mkdirSync(dir, { recursive: true });
            minimalLog(`Created directory: ${dir}`);
        } catch (err) {
            minimalLog(`Error creating directory ${dir}: ${err.message}`, true);
        }
    }
});

// Simple in-memory cache for recently processed files
const extractionCache = new Map();

// Track active Python processes to prevent system overload
const activeProcesses = new Set();
const processingQueue = [];

// Try to load config or get default value
let questionsPerPage = 3;
try {
    // Get configuration from environment variables first
    if (process.env.QUESTIONS_PER_PAGE) {
        questionsPerPage = parseInt(process.env.QUESTIONS_PER_PAGE, 10) || 3;
    } else {
        // Try to read from config file
        try {
            const configPath = path.resolve(__dirname, './src/config.js');
            if (fs.existsSync(configPath)) {
                const config = require('./src/config');
                questionsPerPage = config.questionsPerPage || 3;
            }
        } catch (err) {
            minimalLog("Couldn't load config, using default questions per page: " + err.message);
        }
    }
} catch (err) {
    minimalLog("Error getting questions per page: " + err.message, true);
}

/**
 * Generate a hash for a file to use as cache key
 * @param {string} filePath - Path to the file
 * @returns {string} Hash of the file
 */
function generateFileHash(filePath) {
    try {
        // Get file stats for last modified time and size
        const stats = fs.statSync(filePath);
        const lastModified = stats.mtimeMs;
        const fileSize = stats.size;
        
        // Use file path + modified time + size as the cache key
        const key = `${filePath}:${lastModified}:${fileSize}:${questionsPerPage}`;
        return crypto.createHash('md5').update(key).digest('hex');
    } catch (err) {
        minimalLog('Error generating file hash: ' + err.message, true);
        // Fallback to just the file path if stats fail
        return crypto.createHash('md5').update(filePath).digest('hex');
    }
}

// Custom log function to minimize output with file logging
function minimalLog(message, isError = false) {
    // Timestamp for logs
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    
    // Log to console for errors or if debug mode
    if (isError || process.env.DEBUG_MODE === 'true') {
        console.log(isError ? `ERROR: ${message}` : `INFO: ${message}`);
    }
    
    // Always log to file
    try {
        const logFile = path.join(CONFIG.logsDir, isError ? 'extraction_errors.log' : 'extraction.log');
        fs.appendFileSync(logFile, logMessage + '\n');
    } catch (err) {
        // Fail silently if unable to write to log file
        if (isError) {
            console.error(`Failed to write to log file: ${err.message}`);
        }
    }
}

/**
 * Check if a file is an image based on its extension
 * @param {string} filePath - Path to the file
 * @returns {boolean} - True if the file is an image
 */
function isImageFile(filePath) {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif'];
    const ext = path.extname(filePath).toLowerCase();
    return imageExtensions.includes(ext);
}

/**
 * Pre-process image to make it more suitable for OCR
 * @param {string} imagePath - Path to the image file
 * @returns {Promise<string>} - Path to the processed image
 */
async function preprocessImage(imagePath) {
    try {
        if (!fs.existsSync(imagePath)) {
            throw new Error(`Image file not found: ${imagePath}`);
        }

        const stats = fs.statSync(imagePath);
        if (stats.size === 0) {
            throw new Error(`Image file is empty: ${imagePath}`);
        }

        // Log large file processing without mentioning any size limit
        if (stats.size > CONFIG.largeImageSizeThresholdMB * 1024 * 1024) {
            minimalLog(`Large image detected (${Math.round(stats.size / 1024 / 1024)}MB). This may take longer to process.`);
        }

        return imagePath;
    } catch (error) {
        minimalLog(`Image preprocessing error: ${error.message}`, true);
        throw error;
    }
}

/**
 * Process the next item in the queue if available and under the concurrent limit
 */
function processNextInQueue() {
    // Process multiple items in parallel if possible
    const availableSlots = CONFIG.maxConcurrentProcesses - activeProcesses.size;
    if (availableSlots <= 0 || processingQueue.length === 0) return;
    
    // Process up to availableSlots items at once
    const itemsToProcess = Math.min(availableSlots, processingQueue.length);
    
    for (let i = 0; i < itemsToProcess; i++) {
        if (processingQueue.length > 0) {
            const { filePath, resolve, reject } = processingQueue.shift();
            _extractWithPython(filePath, resolve, reject);
        }
    }
}

/**
 * Main extraction function that handles queueing
 * @param {string} filePath - Path to the file to extract
 * @returns {Promise<{text: string, pageCount: number, questionCount: number, extractedTextPath: string}>} Extracted text and metadata
 */
async function extractWithPython(filePath) {
    // Generate a cache key based on the file
    const cacheKey = generateFileHash(filePath);
    
    // Check if we have this file in cache
    if (extractionCache.has(cacheKey)) {
        const cachedResult = extractionCache.get(cacheKey);
        minimalLog(`Found cached extraction result for ${path.basename(filePath)}`);
        
        // Return cached result if it's not expired
        if (cachedResult.timestamp > Date.now() - CONFIG.cacheTTL) {
            return cachedResult.data;
        } else {
            // Remove expired cache entry
            extractionCache.delete(cacheKey);
        }
    }
    
    // Pre-process the file if it's an image
    if (isImageFile(filePath)) {
        try {
            filePath = await preprocessImage(filePath);
        } catch (error) {
            minimalLog(`Image preprocessing failed: ${error.message}`, true);
            // Continue with original file path
        }
    }
    
    return new Promise((resolve, reject) => {
        // Add to processing queue and process if possible
        processingQueue.push({ filePath, resolve, reject });
        processNextInQueue();
    });
}

/**
 * Internal extraction function that does the actual work
 * @param {string} filePath - Path to the file to extract
 * @param {Function} resolve - Promise resolve function
 * @param {Function} reject - Promise reject function
 */
function _extractWithPython(filePath, resolve, reject) {
    // Track this process
    const processId = crypto.randomBytes(4).toString('hex');
    activeProcesses.add(processId);
    
    try {
        minimalLog(`Using Python extraction for ${path.extname(filePath)} file`);
        
        // Get the path to the Python script in the same directory as this file
        const scriptPath = path.resolve(__dirname, CONFIG.extractionScriptName);
        
        // Check if the extraction script exists
        if (!fs.existsSync(scriptPath)) {
            throw new Error(`Python extraction script not found at ${scriptPath}`);
        }
        
        // Verify the file exists to avoid nonsense extraction
        if (!fs.existsSync(filePath)) {
            throw new Error(`File not found: ${filePath}`);
        }
        
        // Get file size for validation
        const stats = fs.statSync(filePath);
        if (stats.size === 0) {
            throw new Error(`File is empty: ${filePath}`);
        }
        
        // Create output file paths
        const tempFileName = `extract_${path.basename(filePath)}_${processId}`;
        const outputPath = path.join(CONFIG.tempDir, `${tempFileName}.json`);
        const errorPath = path.join(CONFIG.tempDir, `${tempFileName}.error`);
        
        // Prepare command arguments - pass filePath directly as the first argument
        const args = [
            scriptPath,
            filePath
        ];
        
        minimalLog(`Spawning Python process: ${CONFIG.pythonPath} ${args.join(' ')}`);
        
        // Use spawn instead of exec for better handling of large outputs
        const pythonProcess = spawn(CONFIG.pythonPath, args, {
            // Pass through environment variables
            env: { ...process.env },
            // Set timeout
            timeout: CONFIG.extractionTimeout
        });
        
        // Track output for debugging
        let stdoutData = '';
        let stderrData = '';
        
        pythonProcess.stdout.on('data', (data) => {
            stdoutData += data.toString();
            // Only log if there's important output
            if (data.toString().trim() && !data.toString().includes('Processing')) {
                minimalLog(`Python stdout: ${data.toString().trim()}`);
            }
        });
        
        pythonProcess.stderr.on('data', (data) => {
            stderrData += data.toString();
            minimalLog(`Python stderr: ${data.toString().trim()}`, true);
        });
        
        // Handle process completion
        pythonProcess.on('close', (code) => {
            // Remove from active processes
            activeProcesses.delete(processId);
            
            // Process next in queue
            processNextInQueue();
            
            if (code !== 0) {
                // Non-zero exit code indicates error
                minimalLog(`Python process exited with code ${code}`, true);
                
                // Check if error output file exists and read it
                if (fs.existsSync(errorPath)) {
                    try {
                        const errorData = fs.readFileSync(errorPath, 'utf-8');
                        minimalLog(`Python error: ${errorData}`, true);
                        cleanupTemporaryFiles(null, errorPath);
                        reject(new Error(`Extraction failed: ${errorData}`));
                    } catch (readError) {
                        minimalLog(`Failed to read error file: ${readError.message}`, true);
                        reject(new Error(`Extraction failed with code ${code}. stderr: ${stderrData}`));
                    }
                } else {
                    reject(new Error(`Extraction failed with code ${code}. stderr: ${stderrData}`));
                }
                return;
            }
            
            // Parse stdout output as JSON directly
            if (stdoutData && stdoutData.trim()) {
                try {
                    // Find valid JSON in the output - try different approaches
                    let result = null;
                    
                    // First, try to find a complete JSON object using regex
                    const jsonMatch = stdoutData.match(/(\{.*\})/s);
                    if (jsonMatch && jsonMatch[1]) {
                        try {
                            result = JSON.parse(jsonMatch[1]);
                        } catch (e) {
                            minimalLog(`Failed to parse first JSON match: ${e.message}`, true);
                        }
                    }
                    
                    // If that fails, try line by line to find a valid JSON object
                    if (!result) {
                        const lines = stdoutData.split('\n');
                        for (const line of lines) {
                            if (line.trim().startsWith('{') && line.trim().endsWith('}')) {
                                try {
                                    result = JSON.parse(line.trim());
                                    if (result) break;
                                } catch (e) {
                                    // Continue to next line
                                }
                            }
                        }
                    }
                    
                    // If still no result, try one more approach: look for JSON in mixed output
                    if (!result) {
                        // Look for content between curly braces
                        let openBraceIndex = stdoutData.indexOf('{');
                        let balance = 1;
                        let closeBraceIndex = -1;
                        
                        if (openBraceIndex !== -1) {
                            for (let i = openBraceIndex + 1; i < stdoutData.length; i++) {
                                if (stdoutData[i] === '{') balance++;
                                if (stdoutData[i] === '}') balance--;
                                
                                if (balance === 0) {
                                    closeBraceIndex = i;
                                    break;
                                }
                            }
                            
                            if (closeBraceIndex !== -1) {
                                try {
                                    const jsonString = stdoutData.substring(openBraceIndex, closeBraceIndex + 1);
                                    result = JSON.parse(jsonString);
                                } catch (e) {
                                    minimalLog(`Failed to parse balanced JSON: ${e.message}`, true);
                                }
                            }
                        }
                    }
                    
                    // If we found a valid result
                    if (result) {
                        // Add to cache
                        if (Object.keys(extractionCache).length >= CONFIG.maxCacheSize) {
                            // Remove oldest entry if cache is full
                            const oldestKey = [...extractionCache.keys()][0];
                            extractionCache.delete(oldestKey);
                        }
                        
                        // Store in cache
                        const cacheKey = generateFileHash(filePath);
                        extractionCache.set(cacheKey, {
                            timestamp: Date.now(),
                            data: result
                        });
                        
                        // Resolve with extracted data
                        resolve(result);
                    } else {
                        minimalLog(`No valid JSON found in Python stdout`, true);
                        minimalLog(`Raw stdout: ${stdoutData}`, true);
                        reject(new Error(`Failed to parse extraction output: No valid JSON found`));
                    }
                } catch (jsonError) {
                    minimalLog(`Failed to parse extraction output: ${jsonError.message}`, true);
                    minimalLog(`Raw stdout: ${stdoutData}`, true);
                    reject(new Error(`Failed to parse extraction output: ${jsonError.message}`));
                }
            } else {
                minimalLog(`No output from Python process`, true);
                reject(new Error(`No output from Python extraction process`));
            }
        });
        
        // Handle process error
        pythonProcess.on('error', (error) => {
            activeProcesses.delete(processId);
            processNextInQueue();
            minimalLog(`Python process error: ${error.message}`, true);
            reject(new Error(`Python process error: ${error.message}`));
        });
        
    } catch (error) {
        // Remove from active processes
        activeProcesses.delete(processId);
        processNextInQueue();
        minimalLog(`Extraction error: ${error.message}`, true);
        reject(error);
    }
}

/**
 * Clean up temporary files
 * @param {string} inputPath - Path to input file
 * @param {string} outputPath - Path to output file
 */
function cleanupTemporaryFiles(inputPath, outputPath) {
    // Remove output file if it exists
    if (outputPath && fs.existsSync(outputPath)) {
        try {
            fs.unlinkSync(outputPath);
        } catch (err) {
            minimalLog(`Error removing output file ${outputPath}: ${err.message}`, true);
        }
    }
}

/**
 * Check if Python is installed and available
 * @returns {Promise<boolean>} True if Python is available
 */
async function checkPythonInstalled() {
    return new Promise((resolve) => {
        const pythonPath = CONFIG.pythonPath;
        
        // Try to run Python with version flag
        exec(`${pythonPath} --version`, (error, stdout, stderr) => {
            if (error) {
                minimalLog(`Python check failed: ${error.message}`, true);
                resolve(false);
            } else {
                const version = stdout.trim() || stderr.trim();
                minimalLog(`Python version detected: ${version}`);
                resolve(true);
            }
        });
    });
}

/**
 * Get the current status of the extraction service
 * @returns {Object} Status information
 */
function getExtractionStatus() {
    return {
        activeProcesses: activeProcesses.size,
        queueLength: processingQueue.length,
        cacheSize: extractionCache.size,
        maxConcurrentProcesses: CONFIG.maxConcurrentProcesses,
        pythonPath: CONFIG.pythonPath,
        extractionScriptPath: path.resolve(__dirname, CONFIG.extractionScriptName)
    };
}

/**
 * Reset the extraction service state
 */
function resetExtractionService() {
    // Clear cache
    extractionCache.clear();
    
    // Clear queue but don't terminate active processes
    processingQueue.length = 0;
    
    minimalLog('Extraction service reset');
    return { success: true };
}

// Check Python on startup
checkPythonInstalled().then(available => {
    if (!available) {
        minimalLog('WARNING: Python is not available. Extraction may fail.', true);
    }
});

// Exports
module.exports = {
    extractWithPython,
    checkPythonInstalled,
    getExtractionStatus,
    resetExtractionService,
    isImageFile,
    CONFIG // Export config for external use
}; 