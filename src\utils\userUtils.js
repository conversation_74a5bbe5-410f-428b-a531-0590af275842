// User management utilities
const fs = require('fs');
const path = require('path');
const config = require('../config');

// Ensure data directory exists
const dataDir = path.join(__dirname, '../../data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// Create users.json with the new structure if it doesn't exist
const usersPath = path.join(dataDir, 'users.json');
if (!fs.existsSync(usersPath)) {
  fs.writeFileSync(usersPath, JSON.stringify({ count: 0, users: [] }, null, 2));
}

/**
 * Check if user is an admin
 * @param {string|number} userId User ID
 * @returns {Promise<boolean>} Whether the user is an admin
 */
async function isAdmin(userId) {
  try {
    // Convert userId to string for comparison
    userId = String(userId);
    
    // Check if user ID is in the admins list from config
    return config.admins.includes(userId);
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

/**
 * Store user in database
 * @param {Object} user User object from Telegram
 * @returns {Promise<boolean>} Success
 */
async function storeUser(user) {
  if (!user || !user.id) {
    return false;
  }
  
  try {
    // Read existing users data
    let userData = { count: 0, users: [] };
    try {
      const data = fs.readFileSync(usersPath, 'utf8');
      userData = JSON.parse(data);
      
      // Ensure the structure is correct
      if (!userData.users) {
        userData = { count: 0, users: [] };
      }
    } catch (error) {
      console.error('Error reading users file:', error);
      userData = { count: 0, users: [] };
    }
    
    // Check if user already exists
    const userIndex = userData.users.findIndex(u => u.id === user.id);
    
    // Get current date in ISO format
    const currentDate = new Date().toISOString();
    
    if (userIndex === -1) {
      // Add new user
      const newUser = {
        id: user.id,
        username: user.username || '',
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        full_name: `${user.first_name || ''} ${user.last_name || ''}`.trim(),
        language_code: user.language_code || '',
        is_bot: user.is_bot || false,
        joined_at: currentDate,
        last_activity: currentDate
      };
      
      userData.users.push(newUser);
      userData.count = userData.users.length;
      
      console.log(`New user stored: ${user.id} (${user.username || user.first_name})`);
    } else {
      // Update existing user
      userData.users[userIndex] = {
        ...userData.users[userIndex],
        username: user.username || userData.users[userIndex].username,
        first_name: user.first_name || userData.users[userIndex].first_name,
        last_name: user.last_name || userData.users[userIndex].last_name,
        full_name: `${user.first_name || ''} ${user.last_name || ''}`.trim() || userData.users[userIndex].full_name,
        language_code: user.language_code || userData.users[userIndex].language_code,
        last_activity: currentDate
      };
      
      console.log(`Updated user: ${user.id} (${user.username || user.first_name})`);
    }
    
    // Save updated users
    fs.writeFileSync(usersPath, JSON.stringify(userData, null, 2));
    return true;
  } catch (error) {
    console.error('Error storing user:', error);
    return false;
  }
}

/**
 * Log user activity
 * @param {string|number} userId User ID
 * @param {string} action Action performed
 * @returns {Promise<boolean>} Success
 */
async function logUser(userId, action) {
  try {
    console.log(`[${new Date().toISOString()}] User ${userId} performed action: ${action}`);
    
    // Update last activity in users.json
    let userData = { count: 0, users: [] };
    try {
      const data = fs.readFileSync(usersPath, 'utf8');
      userData = JSON.parse(data);
      
      // Ensure the structure is correct
      if (!userData.users) {
        userData = { count: 0, users: [] };
      }
    } catch (error) {
      console.error('Error reading users file:', error);
      return false;
    }
    
    // Current date in ISO format
    const currentDate = new Date().toISOString();
    
    // Find and update user
    const userIndex = userData.users.findIndex(u => u.id === userId);
    if (userIndex !== -1) {
      userData.users[userIndex].last_activity = currentDate;
      userData.users[userIndex].last_action = action;
      
      // Save updated users
      fs.writeFileSync(usersPath, JSON.stringify(userData, null, 2));
    }
    
    return true;
  } catch (error) {
    console.error('Error logging user:', error);
    return false;
  }
}

/**
 * Get total user count
 * @returns {Promise<number>} Total number of users
 */
async function getTotalUsers() {
  try {
    const data = fs.readFileSync(usersPath, 'utf8');
    const userData = JSON.parse(data);
    return userData.count || (userData.users ? userData.users.length : 0);
  } catch (error) {
    console.error('Error getting total users:', error);
    return 0;
  }
}

/**
 * Get all users
 * @returns {Promise<Array>} Array of users
 */
async function getAllUsers() {
  try {
    const data = fs.readFileSync(usersPath, 'utf8');
    const userData = JSON.parse(data);
    return userData.users || [];
  } catch (error) {
    console.error('Error getting all users:', error);
    return [];
  }
}

// Migrate existing users.json to new format if needed
function migrateUsersFile() {
  try {
    const data = fs.readFileSync(usersPath, 'utf8');
    let users = JSON.parse(data);
    
    // Check if it's already in the new format
    if (users && typeof users === 'object' && 'count' in users && 'users' in users) {
      // Already migrated to new structure, now check for timestamp dates
      let migratedCount = 0;
      
      // Update each user's timestamp fields to ISO strings if needed
      if (Array.isArray(users.users)) {
        users.users = users.users.map(user => {
          const updatedUser = { ...user };
          
          // Check if joined_at is a timestamp (number)
          if (typeof user.joined_at === 'number') {
            updatedUser.joined_at = new Date(user.joined_at).toISOString();
            migratedCount++;
          }
          
          // Check if last_activity is a timestamp (number)
          if (typeof user.last_activity === 'number') {
            updatedUser.last_activity = new Date(user.last_activity).toISOString();
            migratedCount++;
          }
          
          return updatedUser;
        });
        
        if (migratedCount > 0) {
          fs.writeFileSync(usersPath, JSON.stringify(users, null, 2));
          console.log(`Migrated ${migratedCount} timestamp fields to ISO date strings`);
        }
      }
      
      return; // Structure migration already complete
    }
    
    // If it's an array, convert to new format
    if (Array.isArray(users)) {
      // Convert to the new structure format
      const newFormat = {
        count: users.length,
        users: users
      };
      
      // Also convert any timestamp dates to ISO strings
      newFormat.users = newFormat.users.map(user => {
        const updatedUser = { ...user };
        
        if (typeof user.joined_at === 'number') {
          updatedUser.joined_at = new Date(user.joined_at).toISOString();
        }
        
        if (typeof user.last_activity === 'number') {
          updatedUser.last_activity = new Date(user.last_activity).toISOString();
        }
        
        return updatedUser;
      });
      
      fs.writeFileSync(usersPath, JSON.stringify(newFormat, null, 2));
      console.log('Successfully migrated users.json to new format with ISO dates');
    } else {
      // Invalid format, create new
      fs.writeFileSync(usersPath, JSON.stringify({ count: 0, users: [] }, null, 2));
      console.log('Created new users.json with correct format');
    }
  } catch (error) {
    console.error('Error migrating users file:', error);
    // Create new file with correct format
    fs.writeFileSync(usersPath, JSON.stringify({ count: 0, users: [] }, null, 2));
  }
}

// Run migration on module load
migrateUsersFile();

module.exports = {
  isAdmin,
  storeUser,
  logUser,
  getTotalUsers,
  getAllUsers
}; 