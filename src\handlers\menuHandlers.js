// Menu handlers for bot interactions
const { Markup } = require('telegraf');
const { isAdmin } = require('../utils/userUtils');
const { API_KEY } = require('../config/config');
const { getCurrentKeyIndices } = require('../services/apiService');

/**
 * Send the main menu for question type selection
 * @param {object} ctx - Telegram context
 * @returns {Promise<object>} Message response
 */
function sendMenu(ctx) {
  return ctx.reply(
    'اختر نوع السؤال:',
    {
      reply_markup: {
        inline_keyboard: [
          [
            Markup.button.callback('متعدد الاختيارات', 'MCQ'),
            Markup.button.callback('صح أو خطأ', 'TF')
          ],
          [
            Markup.button.callback('❓ كيفية عمل البوت', 'how_bot_works')
          ]
        ]
      }
    }
  );
}

/**
 * Send the admin panel with administrative options
 * @param {object} ctx - Telegram context
 * @returns {Promise<object|void>} Message response or void if not admin
 */
async function sendAdminPanel(ctx) {
  // Only proceed if user is an admin
  if (!isAdmin(ctx.from.id)) {
    return;
  }

  return ctx.reply(
    'لوحة التحكم الإدارية:',
    {
      reply_markup: {
        inline_keyboard: [
          [
            Markup.button.callback('إحصائيات', 'admin_stats'),
            Markup.button.callback('تنظيف الكاش', 'admin_clean_cache')
          ],
          [
            Markup.button.callback('إدارة المفتاح', 'admin_keys'),
            Markup.button.callback('حالة النماذج', 'admin_model_status')
          ],
          [
            Markup.button.callback('اختبار النماذج', 'admin_test_models'),
            Markup.button.callback('تشغيل', 'admin_start')
          ]
        ]
      }
    }
  );
}

/**
 * Send the API key management panel
 * @param {object} ctx - Telegram context
 * @returns {Promise<object|void>} Message response or void if not admin
 */
async function sendKeysPanel(ctx) {
  // Only proceed if user is an admin
  if (!isAdmin(ctx.from.id)) {
    return;
  }

  // Get API key info
  const keyInfo = API_KEY ? 
    `المفتاح الحالي: ${API_KEY.substring(0, 4)}...${API_KEY.substring(API_KEY.length - 4)}` : 
    'لا يوجد مفتاح API';

  return ctx.reply(
    `مفتاح API:\n${keyInfo}\n\nاختر عملية:`,
    {
      reply_markup: {
        inline_keyboard: [
          [
            Markup.button.callback('تحديث المفتاح', 'key_update'),
            Markup.button.callback('إختبار المفتاح', 'key_test')
          ],
          [
            Markup.button.callback('رجوع', 'admin')
          ]
        ]
      }
    }
  );
}

/**
 * Send feedback options with menu
 * @param {object} ctx - Telegram context
 * @param {string} cacheKey - Cache key for reference
 * @returns {Promise<object>} Message response
 */
function sendFeedbackWithMenu(ctx, cacheKey) {
  // Generate a unique feedback ID
  const feedbackId = Date.now().toString(36);
  
  // Set up feedback reference storage
  if (!global.feedbackRefs) global.feedbackRefs = {};
  
  // Store the cache key for reference and log it
  global.feedbackRefs[feedbackId] = cacheKey;
  console.log(`Creating feedback reference: ID=${feedbackId}, cache_key=${cacheKey.substring(0, 10)}...`);
  
  // Auto-cleanup after 1 hour
  setTimeout(() => {
    if (global.feedbackRefs?.[feedbackId]) delete global.feedbackRefs[feedbackId];
  }, 60 * 60 * 1000);
  
  // Send combined feedback and menu message
  return ctx.reply('كيف تقيم جودة هذه الأسئلة؟\n\nاختر نوع أسئلة جديد:', {
    reply_markup: {
      inline_keyboard: [
        [
          Markup.button.callback('👍 جيد', `feedback_good_${feedbackId}`),
          Markup.button.callback('👎 سيء', `feedback_poor_${feedbackId}`)
        ],
        [Markup.button.callback('🤔 اقتراحات', `feedback_suggest_${feedbackId}`)],
        [
          Markup.button.callback('اختيار متعدد', 'MCQ'),
          Markup.button.callback('صح/خطأ', 'TF')
        ],
        [
          Markup.button.callback('❓ كيفية عمل البوت', 'how_bot_works')
        ]
      ]
    }
  });
}

module.exports = {
  sendMenu,
  sendAdminPanel,
  sendKeysPanel,
  sendFeedbackWithMenu
}; 