@echo off
echo Cleaning up unnecessary files...

:: Delete test file
if exist test_pillow.py del test_pillow.py

:: Delete root language files (they are duplicates)
if exist ara.traineddata del ara.traineddata
if exist eng.traineddata del eng.traineddata

:: Remove Python package directories (they're installed system-wide)
if exist pytesseract rmdir /s /q pytesseract
if exist pytesseract-0.3.10.dist-info rmdir /s /q pytesseract-0.3.10.dist-info
if exist packaging rmdir /s /q packaging
if exist packaging-24.2.dist-info rmdir /s /q packaging-24.2.dist-info
if exist PIL rmdir /s /q PIL
if exist Pillow-9.5.0.dist-info rmdir /s /q Pillow-9.5.0.dist-info
if exist fitz rmdir /s /q fitz
if exist PyMuPDF-1.22.5.dist-info rmdir /s /q PyMuPDF-1.22.5.dist-info
if exist docx2txt rmdir /s /q docx2txt
if exist docx2txt-0.8.dist-info rmdir /s /q docx2txt-0.8.dist-info

echo Cleaning complete. You can also manually delete python_installer.exe to save space. 